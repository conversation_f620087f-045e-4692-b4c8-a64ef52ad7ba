/*
 * MSPM0 CAN FD Hardware Abstraction Layer
 * Specific implementation for MSPM0 microcontroller
 * Generated for CANFD_IAHP_BMS project
 */

#include "canfd_iahp_bms.h"
#include "ti_msp_dl_config.h"

/* CAN FD Configuration Parameters */
#define CANFD_NOMINAL_BITRATE    500000U    // 500 kbps nominal bitrate
#define CANFD_DATA_BITRATE      2000000U    // 2 Mbps data bitrate
#define CANFD_NOMINAL_PRESCALER      1U     // Nominal bit timing prescaler
#define CANFD_DATA_PRESCALER         1U     // Data bit timing prescaler

/* Message RAM Configuration */
#define CANFD_MSG_RAM_SIZE         2048U    // Message RAM size in bytes
#define CANFD_STD_FILTER_NUM         16U    // Number of standard filters
#define CANFD_EXT_FILTER_NUM         16U    // Number of extended filters
#define CANFD_TX_BUFFER_NUM          32U    // Number of TX buffers
#define CANFD_RX_BUFFER_NUM          64U    // Number of RX buffers

/* Private variables */
static bool canfd_initialized = false;
static uint32_t canfd_tx_buffer_index = 0;

/* Private function prototypes */
static bool configure_canfd_clocks(void);
static bool configure_canfd_pins(void);
static bool configure_canfd_timing(void);
static bool configure_canfd_filters(void);
static bool configure_canfd_message_ram(void);

/*
 * Initialize CAN FD peripheral for MSPM0
 */
bool canfd_init(void)
{
    if (canfd_initialized) {
        return true;  // Already initialized
    }
    
    // Step 1: Configure clocks
    if (!configure_canfd_clocks()) {
        return false;
    }
    
    // Step 2: Configure GPIO pins
    if (!configure_canfd_pins()) {
        return false;
    }
    
    // Step 3: Reset CAN FD peripheral
    DL_MCAN_reset(CANFD_INST);
    
    // Step 4: Configure bit timing
    if (!configure_canfd_timing()) {
        return false;
    }
    
    // Step 5: Configure message RAM
    if (!configure_canfd_message_ram()) {
        return false;
    }
    
    // Step 6: Configure filters
    if (!configure_canfd_filters()) {
        return false;
    }
    
    // Step 7: Enable CAN FD operation
    DL_MCAN_enableIntr(CANFD_INST, MCAN_INTR_MASK_ALL, 0U);
    DL_MCAN_selectOperationMode(CANFD_INST, MCAN_OPERATION_MODE_NORMAL);
    
    // Wait for operation mode change
    while (MCAN_OPERATION_MODE_NORMAL != DL_MCAN_getOperationMode(CANFD_INST)) {
        // Wait for mode change
    }
    
    canfd_initialized = true;
    return true;
}

/*
 * Configure CAN FD clocks
 */
static bool configure_canfd_clocks(void)
{
    // Enable CAN FD peripheral clock
    DL_MCAN_enablePower(CANFD_INST);
    
    // Configure CAN FD clock source
    // This should be done in system initialization
    // Typically use HFCLK or external crystal
    
    return true;
}

/*
 * Configure CAN FD GPIO pins
 */
static bool configure_canfd_pins(void)
{
    // Configure CAN TX pin
    DL_GPIO_initPeripheralOutputFunction(
        GPIO_CANFD_TX_IOMUX, GPIO_CANFD_TX_IOMUX_FUNC);
    DL_GPIO_setPins(GPIO_CANFD_TX_PORT, GPIO_CANFD_TX_PIN);
    DL_GPIO_enableOutput(GPIO_CANFD_TX_PORT, GPIO_CANFD_TX_PIN);
    
    // Configure CAN RX pin
    DL_GPIO_initPeripheralInputFunction(
        GPIO_CANFD_RX_IOMUX, GPIO_CANFD_RX_IOMUX_FUNC);
    
    return true;
}

/*
 * Configure CAN FD bit timing
 */
static bool configure_canfd_timing(void)
{
    DL_MCAN_BitTimingParams bitTiming;
    
    // Configure nominal bit timing (for arbitration phase)
    bitTiming.nominalBitTimingPrescaler = CANFD_NOMINAL_PRESCALER;
    bitTiming.nominalTimeSeg1 = 13U;  // Time segment 1
    bitTiming.nominalTimeSeg2 = 2U;   // Time segment 2
    bitTiming.nominalSynchJumpWidth = 1U;  // Synchronization jump width
    
    // Configure data bit timing (for data phase in CAN FD)
    bitTiming.dataBitTimingPrescaler = CANFD_DATA_PRESCALER;
    bitTiming.dataTimeSeg1 = 3U;      // Data time segment 1
    bitTiming.dataTimeSeg2 = 1U;      // Data time segment 2
    bitTiming.dataSynchJumpWidth = 1U; // Data synchronization jump width
    
    // Apply bit timing configuration
    DL_MCAN_setBitTime(CANFD_INST, &bitTiming);
    
    return true;
}

/*
 * Configure CAN FD message RAM
 */
static bool configure_canfd_message_ram(void)
{
    DL_MCAN_MsgRAMConfigParams msgRAMConfigParams;
    
    // Configure message RAM layout
    msgRAMConfigParams.flssa = 0U;  // Filter list standard start address
    msgRAMConfigParams.lss = CANFD_STD_FILTER_NUM;  // List size standard
    
    msgRAMConfigParams.flesa = msgRAMConfigParams.flssa + 
                               (msgRAMConfigParams.lss * MCAN_STD_ID_FILTER_SIZE_WORDS * 4U);
    msgRAMConfigParams.lse = CANFD_EXT_FILTER_NUM;  // List size extended
    
    msgRAMConfigParams.txStartAddr = msgRAMConfigParams.flesa + 
                                     (msgRAMConfigParams.lse * MCAN_EXT_ID_FILTER_SIZE_WORDS * 4U);
    msgRAMConfigParams.txBufNum = CANFD_TX_BUFFER_NUM;
    msgRAMConfigParams.txFIFOSize = 0U;  // No TX FIFO
    msgRAMConfigParams.txBufDataSize = MCAN_ELEM_DATA_SIZE_64BYTES;
    
    msgRAMConfigParams.rxFIFO0startAddr = msgRAMConfigParams.txStartAddr + 
                                          (msgRAMConfigParams.txBufNum * MCAN_TX_BUFFER_ELEMENT_SIZE_WORDS * 4U);
    msgRAMConfigParams.rxFIFO0size = CANFD_RX_BUFFER_NUM;
    msgRAMConfigParams.rxFIFO0dataSize = MCAN_ELEM_DATA_SIZE_64BYTES;
    msgRAMConfigParams.rxFIFO0OpMode = 0U;  // Blocking mode
    
    msgRAMConfigParams.rxFIFO1startAddr = msgRAMConfigParams.rxFIFO0startAddr + 
                                          (msgRAMConfigParams.rxFIFO0size * MCAN_RX_BUFFER_ELEMENT_SIZE_WORDS * 4U);
    msgRAMConfigParams.rxFIFO1size = 0U;  // No RX FIFO1
    msgRAMConfigParams.rxFIFO1dataSize = MCAN_ELEM_DATA_SIZE_64BYTES;
    msgRAMConfigParams.rxFIFO1OpMode = 0U;
    
    msgRAMConfigParams.rxBufStartAddr = msgRAMConfigParams.rxFIFO1startAddr;
    msgRAMConfigParams.rxBufDataSize = MCAN_ELEM_DATA_SIZE_64BYTES;
    
    // Apply message RAM configuration
    DL_MCAN_msgRAMConfig(CANFD_INST, &msgRAMConfigParams);
    
    return true;
}

/*
 * Configure CAN FD filters
 */
static bool configure_canfd_filters(void)
{
    DL_MCAN_ExtMsgIDFilterElement extFilter;
    
    // Configure extended ID filter to accept BMS messages
    extFilter.filterType = MCAN_FILTER_TYPE_RANGE;
    extFilter.filterConfig = MCAN_FILTER_CONFIG_STORE_RXFIFO0;
    extFilter.filterID1 = 0x80000000U;  // Start of BMS message range
    extFilter.filterID2 = 0x80000010U;  // End of BMS message range
    
    // Apply filter configuration
    DL_MCAN_addExtMsgIDFilter(CANFD_INST, 0U, &extFilter);
    
    return true;
}

/*
 * Send CAN FD message
 */
bool canfd_send_message(uint32_t id, const uint8_t* data, uint8_t dlc, bool is_fd)
{
    if (!canfd_initialized) {
        return false;
    }
    
    DL_MCAN_TxBufElement txMsg;
    
    // Configure message header
    txMsg.id = id;
    txMsg.rtr = 0U;  // Data frame
    txMsg.xtd = 1U;  // Extended ID
    txMsg.esi = 0U;  // Error state indicator
    txMsg.dlc = dlc;
    txMsg.brs = is_fd ? 1U : 0U;  // Bit rate switching for CAN FD
    txMsg.fdf = is_fd ? 1U : 0U;  // CAN FD format
    txMsg.efc = 0U;  // Event FIFO control
    txMsg.mm = 0U;   // Message marker
    
    // Copy data
    for (uint8_t i = 0; i < dlc && i < 64; i++) {
        txMsg.data[i] = data[i];
    }
    
    // Send message
    uint32_t bufferIndex = canfd_tx_buffer_index % CANFD_TX_BUFFER_NUM;
    DL_MCAN_writeMsgRam(CANFD_INST, MCAN_MEM_TYPE_BUF, bufferIndex, &txMsg);
    DL_MCAN_TXBufTransIntrEnable(CANFD_INST, bufferIndex, 1U);
    
    canfd_tx_buffer_index++;
    
    return true;
}

/*
 * Get CAN FD initialization status
 */
bool canfd_is_initialized(void)
{
    return canfd_initialized;
}

/*
 * CAN FD interrupt handler
 */
void CANFD_INST_IRQHandler(void)
{
    uint32_t intrStatus = DL_MCAN_getIntrStatus(CANFD_INST);
    
    // Handle transmission complete
    if (intrStatus & MCAN_INTR_SRC_TRANS_COMPLETE) {
        DL_MCAN_clearIntrStatus(CANFD_INST, MCAN_INTR_SRC_TRANS_COMPLETE, MCAN_INTR_LINE_NUM_1);
        // Handle transmission complete event
    }
    
    // Handle other interrupts as needed
    // ...
}
