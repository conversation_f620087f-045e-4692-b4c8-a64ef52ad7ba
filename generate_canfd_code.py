#!/usr/bin/env python3
"""
CAN FD Code Generator for MSPM0
Generates C code for broadcasting CAN FD messages based on DBC file
"""

import cantools
import os
from datetime import datetime

def generate_header_file(db, output_dir):
    """Generate header file with message structures and definitions"""
    
    header_content = f"""/*
 * Auto-generated CAN FD header file for MSPM0
 * Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
 * Source DBC: CANFD_IAHP_BMS.dbc
 */

#ifndef CANFD_IAHP_BMS_H
#define CANFD_IAHP_BMS_H

#include <stdint.h>
#include <stdbool.h>

/* CAN FD Message IDs */
"""
    
    # Add message ID definitions
    for message in db.messages:
        if message.senders and 'BMS' in message.senders:
            header_content += f"#define {message.name.upper()}_ID    0x{message.frame_id:08X}U\n"
    
    header_content += "\n/* Message Data Length Codes */\n"
    for message in db.messages:
        if message.senders and 'BMS' in message.senders:
            header_content += f"#define {message.name.upper()}_DLC   {message.length}U\n"
    
    # Add signal bit definitions for status messages
    header_content += "\n/* Status Signal Bit Definitions */\n"
    for message in db.messages:
        if message.senders and 'BMS' in message.senders and 'Status' in message.name:
            for signal in message.signals:
                if 'Status' in signal.name or 'CBS' in signal.name or 'EngMode' in signal.name:
                    header_content += f"\n/* {signal.name} bit definitions */\n"
                    if signal.length <= 16:
                        for i in range(signal.length):
                            header_content += f"#define {signal.name.upper()}_BIT{i}    (1U << {i})\n"
    
    # Add structure definitions
    header_content += "\n/* Message Structure Definitions */\n"
    for message in db.messages:
        if message.senders and 'BMS' in message.senders:
            header_content += f"\ntypedef struct {{\n"
            for signal in message.signals:
                if signal.is_signed:
                    if signal.length <= 8:
                        signal_type = "int8_t"
                    elif signal.length <= 16:
                        signal_type = "int16_t"
                    else:
                        signal_type = "int32_t"
                else:
                    if signal.length <= 8:
                        signal_type = "uint8_t"
                    elif signal.length <= 16:
                        signal_type = "uint16_t"
                    else:
                        signal_type = "uint32_t"
                
                header_content += f"    {signal_type} {signal.name};\n"
            
            header_content += f"}} {message.name}_t;\n"
    
    # Add function declarations
    header_content += "\n/* Function Declarations */\n"
    for message in db.messages:
        if message.senders and 'BMS' in message.senders:
            header_content += f"void pack_{message.name}(const {message.name}_t* data, uint8_t* can_data);\n"
            header_content += f"bool send_{message.name}(const {message.name}_t* data);\n"
    
    header_content += "\n/* Utility Functions */\n"
    header_content += "bool canfd_init(void);\n"
    header_content += "bool canfd_send_message(uint32_t id, const uint8_t* data, uint8_t dlc, bool is_fd);\n"
    
    header_content += "\n#endif /* CANFD_IAHP_BMS_H */\n"
    
    # Write header file
    with open(os.path.join(output_dir, "canfd_iahp_bms.h"), 'w') as f:
        f.write(header_content)

def generate_source_file(db, output_dir):
    """Generate source file with packing and sending functions"""
    
    source_content = f"""/*
 * Auto-generated CAN FD source file for MSPM0
 * Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
 * Source DBC: CANFD_IAHP_BMS.dbc
 */

#include "canfd_iahp_bms.h"
#include "ti_msp_dl_config.h"  // MSPM0 specific includes
#include <string.h>

/* Private function prototypes */
static void pack_signal(uint8_t* data, uint64_t value, uint16_t start_bit, uint8_t length, bool is_big_endian);

"""
    
    # Generate packing functions for each message
    for message in db.messages:
        if message.senders and 'BMS' in message.senders:
            source_content += f"""
/*
 * Pack {message.name} message data
 * Message ID: 0x{message.frame_id:08X}
 * DLC: {message.length} bytes
 */
void pack_{message.name}(const {message.name}_t* data, uint8_t* can_data)
{{
    // Clear the data buffer
    memset(can_data, 0, {message.length});
    
"""
            
            # Add signal packing for each signal
            for signal in message.signals:
                # Convert signal value based on scale and offset
                if signal.scale != 1.0 or signal.offset != 0.0:
                    source_content += f"    // Pack {signal.name} (scale: {signal.scale}, offset: {signal.offset})\n"
                    source_content += f"    uint64_t {signal.name}_raw = (uint64_t)((data->{signal.name} - ({signal.offset})) / {signal.scale});\n"
                else:
                    source_content += f"    // Pack {signal.name}\n"
                    source_content += f"    uint64_t {signal.name}_raw = (uint64_t)(data->{signal.name});\n"
                
                # Determine byte order (Intel format is little endian)
                is_big_endian = "false" if signal.byte_order == 'little_endian' else "true"
                
                source_content += f"    pack_signal(can_data, {signal.name}_raw, {signal.start}, {signal.length}, {is_big_endian});\n\n"
            
            source_content += "}\n"
    
    # Generate sending functions
    for message in db.messages:
        if message.senders and 'BMS' in message.senders:
            source_content += f"""
/*
 * Send {message.name} message via CAN FD
 */
bool send_{message.name}(const {message.name}_t* data)
{{
    uint8_t can_data[{message.length}];
    
    // Pack the message data
    pack_{message.name}(data, can_data);
    
    // Send via CAN FD
    return canfd_send_message({message.name.upper()}_ID, can_data, {message.name.upper()}_DLC, true);
}}
"""
    
    return source_content

def main():
    """Main function to generate CAN FD code"""
    
    # Load DBC file
    try:
        db = cantools.database.load_file('CANFD_IAHP_BMS.dbc')
        print(f"Loaded DBC file with {len(db.messages)} messages")
    except Exception as e:
        print(f"Error loading DBC file: {e}")
        return
    
    # Create output directory
    output_dir = "generated_canfd_code"
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate header file
    generate_header_file(db, output_dir)
    print(f"Generated header file: {output_dir}/canfd_iahp_bms.h")
    
    # Generate source file
    source_content = generate_source_file(db, output_dir)

    # Add utility functions to source content
    source_content += """
/*
 * Pack signal into CAN data buffer
 * data: CAN data buffer
 * value: Signal value to pack
 * start_bit: Start bit position
 * length: Signal length in bits
 * is_big_endian: Byte order (true for Motorola, false for Intel)
 */
static void pack_signal(uint8_t* data, uint64_t value, uint16_t start_bit, uint8_t length, bool is_big_endian)
{
    uint16_t bit_pos = start_bit;

    for (uint8_t i = 0; i < length; i++) {
        uint8_t byte_idx = bit_pos / 8;
        uint8_t bit_idx = bit_pos % 8;

        if (value & (1ULL << i)) {
            data[byte_idx] |= (1U << bit_idx);
        }

        if (is_big_endian) {
            // Motorola format: MSB first
            if (bit_idx == 0) {
                bit_pos += 15;  // Jump to next byte, bit 7
            } else {
                bit_pos--;
            }
        } else {
            // Intel format: LSB first
            bit_pos++;
        }
    }
}

/*
 * Initialize CAN FD peripheral for MSPM0
 * Configure CAN FD with appropriate settings for BMS communication
 */
bool canfd_init(void)
{
    // TODO: Implement MSPM0 specific CAN FD initialization
    // This should include:
    // - Clock configuration
    // - Pin configuration
    // - CAN FD controller setup
    // - Bit timing configuration
    // - Filter configuration

    /* Example initialization sequence:
     * 1. Enable CAN peripheral clock
     * 2. Configure GPIO pins for CAN TX/RX
     * 3. Set bit timing for CAN FD (data and arbitration phase)
     * 4. Configure message RAM
     * 5. Set up filters if needed
     * 6. Enable CAN controller
     */

    return true;  // Return true if initialization successful
}

/*
 * Send CAN FD message
 * id: CAN message ID
 * data: Pointer to data buffer
 * dlc: Data length code
 * is_fd: True for CAN FD, false for classic CAN
 */
bool canfd_send_message(uint32_t id, const uint8_t* data, uint8_t dlc, bool is_fd)
{
    // TODO: Implement MSPM0 specific CAN FD message transmission
    // This should include:
    // - Check if TX buffer is available
    // - Configure message object with ID, DLC, and data
    // - Set CAN FD format flag if is_fd is true
    // - Trigger transmission
    // - Wait for transmission complete or timeout

    /* Example transmission sequence:
     * 1. Check TX buffer availability
     * 2. Write message ID to TX buffer
     * 3. Write DLC and FD format flags
     * 4. Copy data to TX buffer
     * 5. Request transmission
     * 6. Wait for completion
     */

    return true;  // Return true if transmission successful
}

/*
 * Example usage function - demonstrates how to send BMS messages
 */
void example_send_bms_messages(void)
{
    // Initialize CAN FD
    if (!canfd_init()) {
        // Handle initialization error
        return;
    }

    // Example: Send BMS_PackInfo message
    BMS_PackInfo_t pack_info = {
        .BMS_Vpack = 48000,        // 48V in mV
        .BMS_PackVoltage = 48000,  // 48V in mV
        .BMS_PackCurrent = 1000,   // 1A in mA
        .BMS_AvgCurrent = 1000,    // 1A in mA
        .BMS_RSOC = 8000,          // 80% (80.00 * 100)
        .BMS_ASOC = 8000,          // 80% (80.00 * 100)
        .BMS_RC = 10000,           // 10Ah in mAh
        .BMS_FCC = 12000,          // 12Ah in mAh
        .BMS_CycleCount = 100,     // 100 cycles
        .BMS_LearnCycle = 10,      // 10 learn cycles
        // ... set other fields as needed
    };

    send_BMS_PackInfo(&pack_info);

    // Example: Send BMS_Status message
    BMS_Status_t status = {
        .BMS_BatteryStatusLow = 0x0001,   // Set appropriate status bits
        .BMS_BatteryStatusHigh = 0x0000,
        .BMS_PackStatusLow = 0x0000,
        .BMS_PackStatusHigh = 0x0000,
        .BMS_SafetyStatusLow = 0x0000,
        .BMS_SafetyStatusHigh = 0x0000,
        .BMS_WarnStatus = 0x0000,
        .BMS_STStatus = 0x0000,
        .BMS_PFStatusLow = 0x0000,
        .BMS_PFStatusHigh = 0x0000,
        .BMS_CBS0_15 = 0x0000,
        .BMS_StartRSOCmin = 2000,         // 20% (20.00 * 100)
        .BMS_UsageCapacity = 8000,        // 8Ah in mAh
        .BMS_SuccChaCap = 8000,           // 8Ah in mAh
        .BMS_SystemTime = 1234567890,     // Unix timestamp
        .BMS_EngMode = 0x00,              // Engineering mode off
    };

    send_BMS_Status(&status);
}
"""

    # Write complete source file
    with open(os.path.join(output_dir, "canfd_iahp_bms.c"), 'w') as f:
        f.write(source_content)

    print(f"Generated source file: {output_dir}/canfd_iahp_bms.c")

if __name__ == "__main__":
    main()
