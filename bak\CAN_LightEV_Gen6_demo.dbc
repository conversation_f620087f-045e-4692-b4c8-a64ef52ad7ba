VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: VCU BMS


BO_ ********** VECTOR__INDEPENDENT_SIG_MSG: 0 Vector__XXX
 SG_ BMS_02_TB : 0|16@1- (1,0) [-32768|32767] "Unit_uVolt" Vector__XXX

BO_ ********** BMS_02_MEAS_AVG_06: 8 Vector__XXX
 SG_ BMS_02_DC_LINK_VOLTAGE : 48|16@1+ (0.01,37.5) [0|130] "Unit_Volt" Vector__XXX
 SG_ BMS_02_BATTERY_VOLTAGE : 32|16@1+ (0.01,0) [0|130] "Unit_Volt" Vector__XXX
 SG_ BMS_02_BOARD_TEMPERATURE : 16|16@1- (1,0) [-32768|32767] "Unit_Celsius" Vector__XXX
 SG_ BMS_02_CELL_TEMPERATURE_4 : 0|16@1- (0.01,0) [-32768|32767] "Unit_Celsius" Vector__XXX

BO_ ********** BMS_02_MEAS_AVG_05: 6 Vector__XXX
 SG_ BMS_02_CELL_TEMPERATURE_3 : 32|16@1- (0.01,0) [-32768|32767] "Unit_Celsius" Vector__XXX
 SG_ BMS_02_CELL_TEMPERATURE_2 : 16|16@1- (0.01,0) [-32768|32767] "Unit_Celsius" Vector__XXX
 SG_ BMS_02_CELL_TEMPERATURE_1 : 0|16@1- (0.01,0) [-32768|32767] "Unit_Celsius" Vector__XXX

BO_ ********** BMS_01_DIAG_05: 2 Vector__XXX
 SG_ BMS_01_ASW_LINK_OUT : 9|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_01_ASW_FET_STUCK_DISCHARGE : 8|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_01_ASW_FET_STUCK_PRECHARGE : 7|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_01_ASW_FET_STUCK_CHARGE : 6|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_01_ASW_FET_OPEN_DISCHARGE : 5|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_01_ASW_FET_OPEN_PRECHARGE : 4|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_01_ASW_FET_OPEN_CHARGE : 3|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_01_ASW_FET_ERROR_DISCHARGE : 2|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_01_ASW_FET_ERROR_PRECHARGE : 1|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_01_ASW_FET_ERROR_CHARGE : 0|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX

BO_ 2415919512 BMS_01_MEAS_AVG_11: 8 Vector__XXX
 SG_ current_2 : 32|32@1- (1E-007,0) [-600|600] "A" Vector__XXX
 SG_ current_1 : 0|32@1- (1E-007,0) [-600|600] "A" Vector__XXX

BO_ 2415919619 BMS_01_MEAS_AVG_16: 8 Vector__XXX
 SG_ diag_result_deltaT : 48|16@1+ (1,0) [0|65535] "sec" Vector__XXX
 SG_ stat_voltage_deltaT : 32|16@1+ (1,0) [0|65535] "sec" Vector__XXX
 SG_ gpio_data_deltaT : 16|16@1+ (1,0) [0|65535] "sec" Vector__XXX
 SG_ cell_voltage_deltaT : 0|16@1+ (1,0) [0|65535] "sec" Vector__XXX

BO_ 2415919618 BMS_01_MEAS_AVG_15: 4 Vector__XXX
 SG_ Current_CT : 16|16@1+ (1,0) [0|1000] "A" Vector__XXX
 SG_ Voltage_CT : 0|16@1+ (1,0) [0|1000] "V" Vector__XXX

BO_ 2415919617 BMS_01_MEAS_AVG_14: 8 Vector__XXX
 SG_ stat_8 : 48|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ stat_7 : 32|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ stat_6 : 16|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ stat_5 : 0|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX

BO_ 2415919616 BMS_01_MEAS_AVG_13: 6 Vector__XXX
 SG_ stat_4 : 32|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ stat_3 : 16|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ stat_1 : 0|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX

BO_ 2415919513 BMS_01_MEAS_AVG_12: 8 Vector__XXX
 SG_ time_base : 32|32@1+ (1,0) [-600|600] "sec" Vector__XXX
 SG_ coulomb_counting : 0|32@1+ (1E-007,0) [-600|600] "Ah" Vector__XXX

BO_ 2415951876 BMS_BATTERY_SPECS_04: 8 Vector__XXX
 SG_ Battery_MinCharging_Current : 0|32@1+ (1,0) [0|429496] "Unit_uVolt" Vector__XXX

BO_ 2415951875 BMS_BATTERY_SPECS_03: 8 Vector__XXX
 SG_ Battery_Discharging_Efficiency : 32|32@1+ (1,0) [0|429496] "Unit_uVolt" Vector__XXX
 SG_ Battery_MaxCharging_Voltage : 0|32@1+ (1,0) [0|429496] "Unit_uVolt" Vector__XXX

BO_ 2415951874 BMS_BATTERY_SPECS_02: 8 Vector__XXX
 SG_ Battery_Charging_Efficiency : 32|32@1+ (1,0) [0|429496] "Unit_uVolt" Vector__XXX
 SG_ Battery_DischargeCutOff_Voltage : 0|32@1+ (1,0) [0|429496] "Unit_uVolt" Vector__XXX

BO_ 2415951873 BMS_BATTERY_SPECS_01: 8 Vector__XXX
 SG_ Battery_Initial_SOC : 32|32@1+ (1,0) [0|429496] "Unit_uVolt" Vector__XXX
 SG_ Battery_NominalRated_Capacity : 0|32@1+ (1,0) [0|429496] "Unit_uVolt" Vector__XXX

BO_ 2415919526 BMS_02_DIAG_04: 7 Vector__XXX
 SG_ BMS_02_LPCM_FLT_DATA_CMF_CUV : 50|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_LPCM_FLT_DATA_CMF_COV : 49|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_LPCM_FLT_DATA_CMF_CDVN : 48|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_LPCM_FLT_DATA_CMF_CDVP : 47|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_LPCM_FLT_DATA_CMF_GUV : 46|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_LPCM_FLT_DATA_CMF_GOV : 45|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_LPCM_FLT_DATA_CMF_GDVN : 44|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_LPCM_FLT_DATA_CMF_GDVP : 43|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BAT_TEMP_OVUV_DATA_TUV4 : 42|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BAT_TEMP_OVUV_DATA_TUV3 : 41|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BAT_TEMP_OVUV_DATA_TUV2 : 40|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BAT_TEMP_OVUV_DATA_TUV1 : 39|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BAT_TEMP_OVUV_DATA_TOV4 : 38|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BAT_TEMP_OVUV_DATA_TOV3 : 37|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BAT_TEMP_OVUV_DATA_TOV2 : 36|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BAT_TEMP_OVUV_DATA_TOV1 : 35|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_OW_DATA_GPIO8 : 34|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_OW_DATA_GPIO7 : 33|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_OW_DATA_GPIO6 : 32|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_OW_DATA_GPIO5 : 31|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_OW_DATA_GPIO4 : 30|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_OW_DATA_GPIO3 : 29|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_OW_DATA_GPIO2 : 28|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_OW_DATA_GPIO1 : 27|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_RED_DATA_GPIO11 : 26|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_RED_DATA_GPIO10 : 25|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_RED_DATA_GPIO9 : 24|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_RED_DATA_GPIO8 : 23|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_RED_DATA_GPIO7 : 22|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_RED_DATA_GPIO6 : 21|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_RED_DATA_GPIO5 : 20|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_RED_DATA_GPIO4 : 19|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_RED_DATA_GPIO3 : 18|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_RED_DATA_GPIO2 : 17|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_GPIO_RED_DATA_GPIO1 : 16|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL16 : 15|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL15 : 14|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL14 : 13|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL13 : 12|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL12 : 11|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL11 : 10|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL10 : 9|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL9 : 8|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL8 : 7|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL7 : 6|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL6 : 5|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL5 : 4|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL4 : 3|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL3 : 2|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL2 : 1|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_UV_DATA_CELL1 : 0|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX

BO_ 2415919525 BMS_02_DIAG_03: 8 Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL16 : 56|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL15 : 55|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL14 : 54|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL13 : 53|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL12 : 52|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL11 : 51|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL10 : 50|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL9 : 49|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL8 : 48|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL7 : 47|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL6 : 46|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL5 : 45|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL4 : 44|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL3 : 43|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL2 : 42|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OV_DATA_CELL1 : 41|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL16 : 40|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL15 : 39|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL14 : 38|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL13 : 37|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL12 : 36|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL11 : 35|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL10 : 34|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL9 : 33|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL8 : 32|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL7 : 31|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL6 : 30|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL5 : 29|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL4 : 28|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL3 : 27|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL2 : 26|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_OW_DATA_CELL1 : 25|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL16 : 24|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL15 : 23|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL14 : 22|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL13 : 21|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL12 : 20|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL11 : 19|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL10 : 18|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL9 : 17|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL8 : 16|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL7 : 15|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL6 : 14|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL5 : 13|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL4 : 12|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL3 : 11|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL2 : 10|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_CELL_RED_DATA_CELL1 : 9|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BSW_FLT_IMPL_STATUS_FLT9 : 8|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BSW_FLT_IMPL_STATUS_FLT8 : 7|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BSW_FLT_IMPL_STATUS_FLT7 : 6|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BSW_FLT_IMPL_STATUS_FLT6 : 5|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BSW_FLT_IMPL_STATUS_FLT5 : 4|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BSW_FLT_IMPL_STATUS_FLT4 : 3|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BSW_FLT_IMPL_STATUS_FLT3 : 2|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BSW_FLT_IMPL_STATUS_FLT2 : 1|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX
 SG_ BMS_02_BSW_FLT_IMPL_STATUS_FLT1 : 0|1@1+ (1,0) [0|1] "Unit_None" Vector__XXX

BO_ 2415919511 BMS_02_MEAS_AVG_10: 4 Vector__XXX
 SG_ BMS_02_gpio_10 : 16|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_02_gpio_9 : 0|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX

BO_ 2415919510 BMS_02_MEAS_AVG_09: 8 Vector__XXX
 SG_ BMS_02_gpio_8 : 48|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_02_gpio_7 : 32|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_02_gpio_6 : 16|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_02_gpio_5 : 0|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX

BO_ 2415919509 BMS_02_MEAS_AVG_08: 8 Vector__XXX
 SG_ BMS_02_gpio_4 : 48|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_02_gpio_3 : 32|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_02_gpio_2 : 16|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_02_gpio_1 : 0|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX

BO_ 2415919504 BMS_01_MEAS_AVG_10: 4 Vector__XXX
 SG_ BMS_01_gpio_10 : 16|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_01_gpio_9 : 0|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX

BO_ 2415919497 BMS_01_MEAS_AVG_09: 8 Vector__XXX
 SG_ BMS_01_gpio_8 : 48|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_01_gpio_7 : 32|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_01_gpio_6 : 16|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_01_gpio_5 : 0|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX

BO_ 2415919496 BMS_01_MEAS_AVG_08: 8 Vector__XXX
 SG_ BMS_01_gpio_4 : 48|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_01_gpio_3 : 32|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_01_gpio_2 : 16|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX
 SG_ BMS_01_gpio_1 : 0|16@1- (0.00015,1.5) [0|6] "Unit_Volt" Vector__XXX

BO_ 2415919508 BMS_02_MEAS_AVG_03: 8 Vector__XXX
 SG_ BMS_02_CellVolt_AVG13 : 0|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG14 : 16|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG15 : 32|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG16 : 48|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX

BO_ 2415919507 BMS_02_MEAS_AVG_02: 8 Vector__XXX
 SG_ BMS_02_CellVolt_AVG09 : 0|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG10 : 16|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG11 : 32|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG12 : 48|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX

BO_ 2415919506 BMS_02_MEAS_AVG_01: 8 Vector__XXX
 SG_ BMS_02_CellVolt_AVG05 : 0|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG06 : 16|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG07 : 32|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG08 : 48|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX

BO_ 2415919505 BMS_02_MEAS_AVG_00: 8 Vector__XXX
 SG_ BMS_02_CellVolt_AVG01 : 0|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG02 : 16|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG03 : 32|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_02_CellVolt_AVG04 : 48|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX

BO_ 2415919488 BMS_01_MEAS_AVG_00: 8 BMS
 SG_ BMS_01_CellVolt_AVG01 : 0|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt" Vector__XXX
 SG_ BMS_01_CellVolt_AVG02 : 16|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU
 SG_ BMS_01_CellVolt_AVG03 : 32|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU
 SG_ BMS_01_CellVolt_AVG04 : 48|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU

BO_ 2415919489 BMS_01_MEAS_AVG_01: 8 BMS
 SG_ BMS_01_CellVolt_AVG05 : 0|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU
 SG_ BMS_01_CellVolt_AVG06 : 16|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU
 SG_ BMS_01_CellVolt_AVG07 : 32|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU
 SG_ BMS_01_CellVolt_AVG08 : 48|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU

BO_ 2415919490 BMS_01_MEAS_AVG_02: 8 BMS
 SG_ BMS_01_CellVolt_AVG09 : 0|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU
 SG_ BMS_01_CellVolt_AVG10 : 16|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU
 SG_ BMS_01_CellVolt_AVG11 : 32|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU
 SG_ BMS_01_CellVolt_AVG12 : 48|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU

BO_ 2415919491 BMS_01_MEAS_AVG_03: 8 BMS
 SG_ BMS_01_CellVolt_AVG13 : 0|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU
 SG_ BMS_01_CellVolt_AVG14 : 16|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU
 SG_ BMS_01_CellVolt_AVG15 : 32|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU
 SG_ BMS_01_CellVolt_AVG16 : 48|16@1- (0.00015,1.5) [-3.4152|6.41505] "Unit_Volt"  VCU

BO_ 2415919492 BMS_01_MEAS_AVG_04: 8 BMS
 SG_ BMS_01_CC : 32|32@1- (1E-006,0) [-2147.483648|2147.483647] "Unit_uVolt" Vector__XXX
 SG_ BMS_01_CURRNT_AVG01 : 0|32@1- (0.01,0) [-21474836.48|21474836.47] "Unit_Amps" Vector__XXX

BO_ 2415919493 BMS_01_MEAS_AVG_05: 8 BMS
 SG_ BMS_01_TB : 0|16@1- (1,0) [-32768|32767] "Unit_uVolt"  VCU
 SG_ BMS_01_CELL_TEMPERATURE_1 : 16|16@1- (0.01,0) [-32768|32767] "Unit_Celsius"  VCU
 SG_ BMS_01_CELL_TEMPERATURE_2 : 32|16@1- (0.01,0) [-32768|32767] "Unit_Celsius"  VCU
 SG_ BMS_01_CELL_TEMPERATURE_3 : 48|16@1- (0.01,0) [-32768|32767] "Unit_Celsius"  VCU

BO_ 2415919494 BMS_01_MEAS_AVG_06: 8 BMS
 SG_ BMS_01_CELL_TEMPERATURE_4 : 0|16@1- (0.01,0) [-32768|32767] "Unit_Celsius"  VCU
 SG_ BMS_01_BOARD_TEMPERATURE : 16|16@1- (1,0) [-32768|32767] "Unit_Celsius"  VCU
 SG_ BATTERY_VOLTAGE : 32|16@1+ (0.01,0) [0|130] "Unit_Volt"  VCU
 SG_ DC_LINK_VOLTAGE : 48|16@1+ (0.01,37.5) [0|130] "Unit_Volt"  VCU

BO_ 2415919495 BMS_01_MEAS_AVG_07: 8 BMS
 SG_ BMS_01_CT_V : 0|11@1+ (1,0) [0|2047] "Unit_None"  VCU
 SG_ BMS_01_CTS_V : 11|2@1+ (1,0) [0|3] "Unit_None"  VCU
 SG_ BMS_01_CT_I : 13|11@1+ (1,0) [0|2047] "Unit_None"  VCU
 SG_ BMS_01_CTS_I : 24|2@1+ (1,0) [0|3] "Unit_None"  VCU
 SG_ STATUS_FLAG1 : 26|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ STATUS_FLAG2 : 27|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ STATUS_FLAG3 : 28|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ STATUS_FLAG4 : 29|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ STATUS_FLAG5 : 30|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ STATUS_FLAG6 : 31|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ STATUS_FLAG7 : 32|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ STATUS_FLAG8 : 33|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ DIAGNOSTIC_FLAG1 : 34|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ DIAGNOSTIC_FLAG2 : 35|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ DIAGNOSTIC_FLAG3 : 36|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ DIAGNOSTIC_FLAG4 : 37|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ DIAGNOSTIC_FLAG5 : 38|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ DIAGNOSTIC_FLAG6 : 39|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ DIAGNOSTIC_FLAG7 : 40|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ DIAGNOSTIC_FLAG8 : 41|1@1+ (1,0) [0|1] "Unit_None"  VCU

BO_ 2415919440 BMS_01_ALGO_SOC_00: 8 BMS
 SG_ BMS_01_SOC_CELL1 : 0|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL2 : 16|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL3 : 32|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL4 : 48|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU

BO_ 2415919441 BMS_01_ALGO_SOC_01: 8 BMS
 SG_ BMS_01_SOC_CELL5 : 0|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL6 : 16|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL7 : 32|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL8 : 48|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU

BO_ 2415919442 BMS_01_ALGO_SOC_02: 8 BMS
 SG_ BMS_01_SOC_CELL9 : 0|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL10 : 16|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL11 : 32|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL12 : 48|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU

BO_ 2415919443 BMS_01_ALGO_SOC_03: 8 BMS
 SG_ BMS_01_SOC_CELL13 : 0|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL14 : 16|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL15 : 32|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_SOC_CELL16 : 48|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU

BO_ 2415919444 BMS_01_ALGO_SOC_04: 8 BMS
 SG_ BMS_01_ERROR_SOC_CELL1 : 0|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL2 : 16|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL3 : 32|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL4 : 48|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU

BO_ 2415919445 BMS_01_ALGO_SOC_05: 8 BMS
 SG_ BMS_01_ERROR_SOC_CELL5 : 0|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL6 : 16|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL7 : 32|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL8 : 48|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU

BO_ 2415919446 BMS_01_ALGO_SOC_06: 8 BMS
 SG_ BMS_01_ERROR_SOC_CELL9 : 0|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL10 : 16|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL11 : 32|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL12 : 48|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU

BO_ 2415919447 BMS_01_ALGO_SOC_07: 8 BMS
 SG_ BMS_01_ERROR_SOC_CELL13 : 0|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL14 : 16|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL15 : 32|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU
 SG_ BMS_01_ERROR_SOC_CELL16 : 48|16@1+ (0.01,0) [0|100] "Unit_Percent"  VCU

BO_ 2415919472 BMS_01_ALGO_SOP: 8 BMS
 SG_ BMS_01_CHARGE_CURRENT : 48|16@1+ (-0.01,0) [0|1000] "Unit_Amp" Vector__XXX
 SG_ BMS_01_DISCHARGE_CURRENT : 32|16@1+ (0.01,0) [0|1000] "Unit_Amp" Vector__XXX
 SG_ BMS_01_CHARGE_POWER : 16|16@1+ (-0.01,0) [0|1000] "Unit_Watt" Vector__XXX
 SG_ BMS_01_DISHARGE_POWER : 0|16@1+ (0.01,0) [0|1000] "Unit_Watt" Vector__XXX

BO_ 2415919473 BMS_01_ALGO_SOH: 8 BMS
 SG_ deltat : 16|16@1+ (0.01,0) [0|1000] "sec" Vector__XXX
 SG_ BMS_SOC_PACK : 8|8@1+ (1,0) [0|100] "Unit_Amp" Vector__XXX
 SG_ BMS_01_SOH : 0|8@1+ (1,0) [0|100] "Unit_Amp"  VCU

BO_ 2415919520 BMS_01_DIAG_00: 8 BMS
 SG_ BMS_01_SPF_PASS_FAIL_SM1 : 0|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM2 : 1|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM5 : 2|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM7 : 3|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM9 : 4|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM10 : 5|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM14 : 6|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM15 : 7|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM17 : 8|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM19 : 9|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM21 : 10|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM23 : 11|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM24 : 12|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM26 : 13|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM28 : 14|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM29 : 15|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM30 : 16|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM31 : 17|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM33 : 18|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM35 : 19|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM38 : 20|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM39 : 21|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM40 : 22|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM41 : 23|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_SM42 : 24|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_PASS_FAIL_LPF : 25|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM1 : 26|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM2 : 27|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM5 : 28|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM7 : 29|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM9 : 30|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM10 : 31|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM14 : 32|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM15 : 33|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM17 : 34|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM19 : 35|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM21 : 36|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM23 : 37|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM24 : 38|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM26 : 39|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM28 : 40|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM29 : 41|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM30 : 42|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM31 : 43|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM33 : 44|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM35 : 45|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM38 : 46|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM39 : 47|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM40 : 48|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM41 : 49|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_SM42 : 50|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_EXEC_STATUS_LPF : 51|1@1+ (1,0) [0|1] "Unit_None"  VCU

BO_ 2415919521 BMS_01_DIAG_01: 8 BMS
 SG_ BMS_01_SPF_IMPL_STATUS_SM1 : 0|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM2 : 1|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM5 : 2|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM7 : 3|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM9 : 4|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM10 : 5|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM14 : 6|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM15 : 7|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM17 : 8|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM19 : 9|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM21 : 10|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM23 : 11|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM24 : 12|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM26 : 13|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM28 : 14|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM29 : 15|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM30 : 16|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM31 : 17|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM33 : 18|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM35 : 19|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM38 : 20|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM39 : 21|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM40 : 22|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM41 : 23|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_SM42 : 24|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_SPF_IMPL_STATUS_LPF : 25|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT1 : 26|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT2 : 27|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT3 : 28|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT4 : 29|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT5 : 30|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT6 : 31|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT7 : 32|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT8 : 33|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT9 : 34|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT10 : 35|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT11 : 36|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT12 : 37|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT13 : 38|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT14 : 39|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT15 : 40|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT16 : 41|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT17 : 42|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT18 : 43|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT19 : 44|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_PASS_FAIL_FLT20 : 45|1@1+ (1,0) [0|1] "Unit_None"  VCU

BO_ 2415919522 BMS_01_DIAG_02: 8 BMS
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT1 : 0|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT2 : 1|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT3 : 2|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT4 : 3|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT5 : 4|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT6 : 5|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT7 : 6|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT8 : 7|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT9 : 8|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT10 : 9|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT11 : 10|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT12 : 11|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT13 : 12|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT14 : 13|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT15 : 14|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT16 : 15|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT17 : 16|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT18 : 17|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT19 : 18|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_EXEC_STATUS_FLT20 : 19|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT1 : 20|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT2 : 21|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT3 : 22|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT4 : 23|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT5 : 24|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT6 : 25|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT7 : 26|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT8 : 27|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT9 : 28|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT10 : 29|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT11 : 30|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT12 : 31|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT13 : 32|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT14 : 33|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT15 : 34|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT16 : 35|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT17 : 36|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT18 : 37|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT19 : 38|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_ASW_FLT_IMPL_STATUS_FLT20 : 39|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_PASS_FAIL_FLT1 : 40|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_PASS_FAIL_FLT2 : 41|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_PASS_FAIL_FLT3 : 42|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_PASS_FAIL_FLT4 : 43|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_PASS_FAIL_FLT5 : 44|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_PASS_FAIL_FLT6 : 45|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_PASS_FAIL_FLT7 : 46|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_PASS_FAIL_FLT8 : 47|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_PASS_FAIL_FLT9 : 48|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_EXEC_STATUS_FLT1 : 49|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_EXEC_STATUS_FLT2 : 50|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_EXEC_STATUS_FLT3 : 51|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_EXEC_STATUS_FLT4 : 52|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_EXEC_STATUS_FLT5 : 53|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_EXEC_STATUS_FLT6 : 54|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_EXEC_STATUS_FLT7 : 55|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_EXEC_STATUS_FLT8 : 56|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_EXEC_STATUS_FLT9 : 57|1@1+ (1,0) [0|1] "Unit_None"  VCU

BO_ 2415919523 BMS_01_DIAG_03: 8 BMS
 SG_ BMS_01_BSW_FLT_IMPL_STATUS_FLT1 : 0|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_IMPL_STATUS_FLT2 : 1|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_IMPL_STATUS_FLT3 : 2|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_IMPL_STATUS_FLT4 : 3|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_IMPL_STATUS_FLT5 : 4|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_IMPL_STATUS_FLT6 : 5|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_IMPL_STATUS_FLT7 : 6|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_IMPL_STATUS_FLT8 : 7|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BSW_FLT_IMPL_STATUS_FLT9 : 8|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL1 : 9|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL2 : 10|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL3 : 11|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL4 : 12|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL5 : 13|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL6 : 14|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL7 : 15|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL8 : 16|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL9 : 17|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL10 : 18|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL11 : 19|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL12 : 20|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL13 : 21|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL14 : 22|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL15 : 23|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_RED_DATA_CELL16 : 24|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL1 : 25|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL2 : 26|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL3 : 27|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL4 : 28|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL5 : 29|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL6 : 30|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL7 : 31|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL8 : 32|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL9 : 33|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL10 : 34|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL11 : 35|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL12 : 36|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL13 : 37|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL14 : 38|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL15 : 39|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OW_DATA_CELL16 : 40|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL1 : 41|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL2 : 42|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL3 : 43|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL4 : 44|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL5 : 45|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL6 : 46|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL7 : 47|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL8 : 48|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL9 : 49|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL10 : 50|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL11 : 51|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL12 : 52|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL13 : 53|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL14 : 54|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL15 : 55|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_OV_DATA_CELL16 : 56|1@1+ (1,0) [0|1] "Unit_None"  VCU

BO_ 2415919524 BMS_01_DIAG_04: 8 BMS
 SG_ BMS_01_CELL_UV_DATA_CELL1 : 0|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL2 : 1|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL3 : 2|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL4 : 3|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL5 : 4|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL6 : 5|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL7 : 6|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL8 : 7|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL9 : 8|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL10 : 9|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL11 : 10|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL12 : 11|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL13 : 12|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL14 : 13|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL15 : 14|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_CELL_UV_DATA_CELL16 : 15|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_RED_DATA_GPIO1 : 16|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_RED_DATA_GPIO2 : 17|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_RED_DATA_GPIO3 : 18|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_RED_DATA_GPIO4 : 19|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_RED_DATA_GPIO5 : 20|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_RED_DATA_GPIO6 : 21|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_RED_DATA_GPIO7 : 22|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_RED_DATA_GPIO8 : 23|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_RED_DATA_GPIO9 : 24|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_RED_DATA_GPIO10 : 25|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_RED_DATA_GPIO11 : 26|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_OW_DATA_GPIO1 : 27|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_OW_DATA_GPIO2 : 28|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_OW_DATA_GPIO3 : 29|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_OW_DATA_GPIO4 : 30|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_OW_DATA_GPIO5 : 31|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_OW_DATA_GPIO6 : 32|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_OW_DATA_GPIO7 : 33|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_GPIO_OW_DATA_GPIO8 : 34|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BAT_TEMP_OVUV_DATA_TOV1 : 35|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BAT_TEMP_OVUV_DATA_TOV2 : 36|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BAT_TEMP_OVUV_DATA_TOV3 : 37|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BAT_TEMP_OVUV_DATA_TOV4 : 38|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BAT_TEMP_OVUV_DATA_TUV1 : 39|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BAT_TEMP_OVUV_DATA_TUV2 : 40|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BAT_TEMP_OVUV_DATA_TUV3 : 41|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_BAT_TEMP_OVUV_DATA_TUV4 : 42|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPCM_FLT_DATA_CMF_GDVP : 43|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPCM_FLT_DATA_CMF_GDVN : 44|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPCM_FLT_DATA_CMF_GOV : 45|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPCM_FLT_DATA_CMF_GUV : 46|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPCM_FLT_DATA_CMF_CDVP : 47|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPCM_FLT_DATA_CMF_CDVN : 48|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPCM_FLT_DATA_CMF_COV : 49|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPCM_FLT_DATA_CMF_CUV : 50|1@1+ (1,0) [0|1] "Unit_None"  VCU

BO_ 2415919568 BMS_01_LPF_DIAG_00: 8 BMS
 SG_ BMS_01_LPF_PASS_FAIL_SM3 : 0|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM4 : 1|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM6 : 2|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM8 : 3|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM11 : 4|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM12 : 5|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM13 : 6|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM16 : 7|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM18 : 8|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM20 : 9|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM22 : 10|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM25 : 11|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM27 : 12|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM32 : 13|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM34 : 14|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM36 : 15|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_SM37 : 16|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_PASS_FAIL_COULCNTR : 17|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM3 : 18|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM4 : 19|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM6 : 20|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM8 : 21|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM11 : 22|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM12 : 23|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM13 : 24|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM16 : 25|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM18 : 26|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM20 : 27|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM22 : 28|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM25 : 29|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM27 : 30|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM32 : 31|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM34 : 32|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM36 : 33|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_SM37 : 34|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_EXEC_STATUS_COULCNTR : 35|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM3 : 36|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM4 : 37|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM6 : 38|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM8 : 39|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM11 : 40|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM12 : 41|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM13 : 42|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM16 : 43|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM18 : 44|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM20 : 45|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM22 : 46|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM25 : 47|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM27 : 48|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM32 : 49|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM34 : 50|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM36 : 51|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_SM37 : 52|1@1+ (1,0) [0|1] "Unit_None"  VCU
 SG_ BMS_01_LPF_IMPL_STATUS_COULCNTR : 53|1@1+ (1,0) [0|1] "Unit_None"  VCU

BO_ ********** BMS_01_BMS_COMMAND: 8 VCU
 SG_ BMS_01_LATENT_CHK : 0|16@1+ (1,0) [0|65535] "Unit_None"  BMS
 SG_ BMS_01_VEHICLE_STATE : 16|8@1+ (1,0) [0|65535] "Unit_None"  BMS
 SG_ BMS_01_DEBUG_MSG : 32|24@1+ (1,0) [0|16777215] "Unit_None"  BMS
 SG_ BMS_01_VEHICLE_RESET : 56|1@1+ (1,0) [0|1] "Unit_None"  BMS



CM_ BO_ ********** "This is a message for not used signals, created by Vector CANdb++ DBC OLE DB Provider.";
CM_ SG_ ********** BMS_02_TB "Raw accumulated time base";
CM_ SG_ ********** BMS_02_DC_LINK_VOLTAGE "Raw GPIO voltage";
CM_ SG_ ********** BMS_02_BATTERY_VOLTAGE "Raw GPIO voltage";
CM_ SG_ ********** BMS_02_BOARD_TEMPERATURE "Raw GPIO voltage";
CM_ SG_ ********** BMS_02_CELL_TEMPERATURE_4 "Raw GPIO voltage";
CM_ SG_ ********** BMS_02_CELL_TEMPERATURE_3 "Raw GPIO voltage";
CM_ SG_ ********** BMS_02_CELL_TEMPERATURE_2 "Raw GPIO voltage";
CM_ SG_ ********** BMS_02_CELL_TEMPERATURE_1 "Raw GPIO voltage";
CM_ SG_ ********** BMS_01_ASW_LINK_OUT "ASW LINK OUT
0: FLT Passed
1: FLT Failed";
CM_ SG_ ********** BMS_01_ASW_FET_STUCK_DISCHARGE "ASW FET Stuck - Discharge
0: FLT Passed
1: FLT Failed";
CM_ SG_ ********** BMS_01_ASW_FET_STUCK_PRECHARGE "ASW FET Stuck - Pre-charge
0: FLT Passed
1: FLT Failed";
CM_ SG_ ********** BMS_01_ASW_FET_STUCK_CHARGE "ASW FET Stuck - Charge
0: FLT Passed
1: FLT Failed";
CM_ SG_ ********** BMS_01_ASW_FET_OPEN_DISCHARGE "ASW FET Open - Discharge
0: FLT Passed
1: FLT Failed";
CM_ SG_ ********** BMS_01_ASW_FET_OPEN_PRECHARGE "ASW FET Open - Pre-charge
0: FLT Passed
1: FLT Failed";
CM_ SG_ ********** BMS_01_ASW_FET_OPEN_CHARGE "ASW FET Open - Charge
0: FLT Passed
1: FLT Failed";
CM_ SG_ ********** BMS_01_ASW_FET_ERROR_DISCHARGE "ASW FET Error - Discharge
0: FLT Passed
1: FLT Failed";
CM_ SG_ ********** BMS_01_ASW_FET_ERROR_PRECHARGE "ASW FET Error - Pre-charge
0: FLT Passed
1: FLT Failed";
CM_ SG_ ********** BMS_01_ASW_FET_ERROR_CHARGE "ASW FET Error - Charge
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919512 current_2 "Current";
CM_ SG_ 2415919512 current_1 "Current";
CM_ SG_ 2415919619 diag_result_deltaT "DeltaT";
CM_ SG_ 2415919619 stat_voltage_deltaT "DeltaT";
CM_ SG_ 2415919619 gpio_data_deltaT "DeltaT";
CM_ SG_ 2415919619 cell_voltage_deltaT "DeltaT";
CM_ SG_ 2415919618 Current_CT "Current CT";
CM_ SG_ 2415919618 Voltage_CT "Voltage CT";
CM_ SG_ 2415919617 stat_8 "Stat Voltage";
CM_ SG_ 2415919617 stat_7 "Stat Voltage";
CM_ SG_ 2415919617 stat_6 "Stat Voltage";
CM_ SG_ 2415919617 stat_5 "Stat Voltage";
CM_ SG_ 2415919616 stat_4 "Stat Voltage. Battery Voltage in BMS_01_MEAS_AVG_06";
CM_ SG_ 2415919616 stat_3 "Stat Voltage. Battery Voltage in BMS_01_MEAS_AVG_06";
CM_ SG_ 2415919616 stat_1 "Stat Voltage. Battery Voltage in BMS_01_MEAS_AVG_06";
CM_ SG_ 2415919513 time_base "Current";
CM_ SG_ 2415919513 coulomb_counting "Current";
CM_ SG_ 2415951876 Battery_MinCharging_Current "User Input - Battery Minimum Charging Current";
CM_ SG_ 2415951875 Battery_Discharging_Efficiency "User Input - Battery Discharge Efficiency";
CM_ SG_ 2415951875 Battery_MaxCharging_Voltage "User Input - Battery Maximum Charging Current";
CM_ SG_ 2415951874 Battery_Charging_Efficiency "User Input - Battery Charging Efficiency";
CM_ SG_ 2415951874 Battery_DischargeCutOff_Voltage "User Input - Battery Discharge Cut-off Voltage";
CM_ SG_ 2415951873 Battery_Initial_SOC "User Input - Battery Initial State of Charge (SOC)";
CM_ SG_ 2415951873 Battery_NominalRated_Capacity "User Input - Battery Nominal Rated Capacity";
CM_ SG_ 2415919526 BMS_02_LPCM_FLT_DATA_CMF_CUV "LPCM mode Fault Monitoring Flag - Cell Under Voltage";
CM_ SG_ 2415919526 BMS_02_LPCM_FLT_DATA_CMF_COV "LPCM mode Fault Monitoring Flag - Cell Over Voltage";
CM_ SG_ 2415919526 BMS_02_LPCM_FLT_DATA_CMF_CDVN "LPCM mode Cell Delta Voltage in Negative Direction";
CM_ SG_ 2415919526 BMS_02_LPCM_FLT_DATA_CMF_CDVP "LPCM mode Cell Delta Voltage in Positive Direction";
CM_ SG_ 2415919526 BMS_02_LPCM_FLT_DATA_CMF_GUV "LPCM mode Fault Monitoring Flag - GPIO Under Voltage";
CM_ SG_ 2415919526 BMS_02_LPCM_FLT_DATA_CMF_GOV "LPCM mode Fault Monitoring Flag - GPIO Over Voltage";
CM_ SG_ 2415919526 BMS_02_LPCM_FLT_DATA_CMF_GDVN "LPCM mode GPIO Delta Voltage in Negative Direction";
CM_ SG_ 2415919526 BMS_02_LPCM_FLT_DATA_CMF_GDVP "LPCM mode GPIO Delta Voltage in Positive Direction";
CM_ SG_ 2415919526 BMS_02_BAT_TEMP_OVUV_DATA_TUV4 "Battery Temperature Sensor Under voltage status
0: No Under Voltage
1: Under voltage";
CM_ SG_ 2415919526 BMS_02_BAT_TEMP_OVUV_DATA_TUV3 "Battery Temperature Sensor Under voltage status
0: No Under Voltage
1: Under voltage";
CM_ SG_ 2415919526 BMS_02_BAT_TEMP_OVUV_DATA_TUV2 "Battery Temperature Sensor Under voltage status
0: No Under Voltage
1: Under voltage";
CM_ SG_ 2415919526 BMS_02_BAT_TEMP_OVUV_DATA_TUV1 "Battery Temperature Sensor Under voltage status
0: No Under Voltage
1: Under voltage";
CM_ SG_ 2415919526 BMS_02_BAT_TEMP_OVUV_DATA_TOV4 "Battery Temperature Sensor Over voltage status
0: No Over Voltage
1: Over voltage";
CM_ SG_ 2415919526 BMS_02_BAT_TEMP_OVUV_DATA_TOV3 "Battery Temperature Sensor Over voltage status
0: No Over Voltage
1: Over voltage";
CM_ SG_ 2415919526 BMS_02_BAT_TEMP_OVUV_DATA_TOV2 "Battery Temperature Sensor Over voltage status
0: No Over Voltage
1: Over voltage";
CM_ SG_ 2415919526 BMS_02_BAT_TEMP_OVUV_DATA_TOV1 "Battery Temperature Sensor Over voltage status
0: No Over Voltage
1: Over voltage";
CM_ SG_ 2415919526 BMS_02_GPIO_OW_DATA_GPIO8 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919526 BMS_02_GPIO_OW_DATA_GPIO7 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919526 BMS_02_GPIO_OW_DATA_GPIO6 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919526 BMS_02_GPIO_OW_DATA_GPIO5 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919526 BMS_02_GPIO_OW_DATA_GPIO4 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919526 BMS_02_GPIO_OW_DATA_GPIO3 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919526 BMS_02_GPIO_OW_DATA_GPIO2 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919526 BMS_02_GPIO_OW_DATA_GPIO1 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919526 BMS_02_GPIO_RED_DATA_GPIO11 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919526 BMS_02_GPIO_RED_DATA_GPIO10 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919526 BMS_02_GPIO_RED_DATA_GPIO9 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919526 BMS_02_GPIO_RED_DATA_GPIO8 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919526 BMS_02_GPIO_RED_DATA_GPIO7 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919526 BMS_02_GPIO_RED_DATA_GPIO6 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919526 BMS_02_GPIO_RED_DATA_GPIO5 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919526 BMS_02_GPIO_RED_DATA_GPIO4 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919526 BMS_02_GPIO_RED_DATA_GPIO3 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919526 BMS_02_GPIO_RED_DATA_GPIO2 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919526 BMS_02_GPIO_RED_DATA_GPIO1 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL16 "Cell Under voltage status for Cell 16
0: No under voltage on Cell 16
1: Under voltage on Cell 16";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL15 "Cell Under voltage status for Cell 15
0: No under voltage on Cell 15
1: Under voltage on Cell 15";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL14 "Cell Under voltage status for Cell 14
0: No under voltage on Cell 14
1: Under voltage on Cell 14";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL13 "Cell Under voltage status for Cell 13
0: No under voltage on Cell 13
1: Under voltage on Cell 13";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL12 "Cell Under voltage status for Cell 12
0: No under voltage on Cell 12
1: Under voltage on Cell 12";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL11 "Cell Under voltage status for Cell 11
0: No under voltage on Cell 11
1: Under voltage on Cell 11";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL10 "Cell Under voltage status for Cell 10
0: No under voltage on Cell 10
1: Under voltage on Cell 10";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL9 "Cell Under voltage status for Cell 9
0: No under voltage on Cell 9
1: Under voltage on Cell 9";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL8 "Cell Under voltage status for Cell 8
0: No under voltage on Cell 8
1: Under voltage on Cell 8";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL7 "Cell Under voltage status for Cell 7
0: No under voltage on Cell 7
1: Under voltage on Cell 7";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL6 "Cell Under voltage status for Cell 6
0: No under voltage on Cell 6
1: Under voltage on Cell 6";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL5 "Cell Under voltage status for Cell 5
0: No under voltage on Cell 5
1: Under voltage on Cell 5";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL4 "Cell Under voltage status for Cell 4
0: No under voltage on Cell 4
1: Under voltage on Cell 4";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL3 "Cell Under voltage status for Cell 3
0: No under voltage on Cell 3
1: Under voltage on Cell 3";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL2 "Cell Under voltage status for Cell 2
0: No under voltage on Cell 2
1: Under voltage on Cell 2";
CM_ SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL1 "Cell Under voltage status for Cell 1
0: No under voltage on Cell 1
1: Under voltage on Cell 1";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL16 "Cell Over voltage status for Cell 16
0: No overvoltage on Cell 16
1: Overvoltage on Cell 16";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL15 "Cell Over voltage status for Cell 15
0: No overvoltage on Cell 15
1: Overvoltage on Cell 15";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL14 "Cell Over voltage status for Cell 14
0: No overvoltage on Cell 14
1: Overvoltage on Cell 14";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL13 "Cell Over voltage status for Cell 13
0: No overvoltage on Cell 13
1: Overvoltage on Cell 13";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL12 "Cell Over voltage status for Cell 12
0: No overvoltage on Cell 12
1: Overvoltage on Cell 12";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL11 "Cell Over voltage status for Cell 11
0: No overvoltage on Cell 11
1: Overvoltage on Cell 11";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL10 "Cell Over voltage status for Cell 10
0: No overvoltage on Cell 10
1: Overvoltage on Cell 10";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL9 "Cell Over voltage status for Cell 9
0: No overvoltage on Cell 9
1: Overvoltage on Cell 9";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL8 "Cell Over voltage status for Cell 8
0: No overvoltage on Cell 8
1: Overvoltage on Cell 8";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL7 "Cell Over voltage status for Cell 7
0: No overvoltage on Cell 7
1: Overvoltage on Cell 7";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL6 "Cell Over voltage status for Cell 6
0: No overvoltage on Cell 6
1: Overvoltage on Cell 6";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL5 "Cell Over voltage status for Cell 5
0: No overvoltage on Cell 5
1: Overvoltage on Cell 5";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL4 "Cell Over voltage status for Cell 4
0: No overvoltage on Cell 4
1: Overvoltage on Cell 4";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL3 "Cell Over voltage status for Cell 3
0: No overvoltage on Cell 3
1: Overvoltage on Cell 3";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL2 "Cell Over voltage status for Cell 2
0: No overvoltage on Cell 2
1: Overvoltage on Cell 2";
CM_ SG_ 2415919525 BMS_02_CELL_OV_DATA_CELL1 "Cell Over voltage status for Cell 1
0: No overvoltage on Cell 1
1: Overvoltage on Cell 1";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL16 "Cell Open Wire result for Cell 16
0: No Open Wire on Cell 16
1: Open Wire on Cell 16";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL15 "Cell Open Wire result for Cell 15
0: No Open Wire on Cell 15
1: Open Wire on Cell 15";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL14 "Cell Open Wire result for Cell 14
0: No Open Wire on Cell 14
1: Open Wire on Cell 14";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL13 "Cell Open Wire result for Cell 13
0: No Open Wire on Cell 13
1: Open Wire on Cell 13";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL12 "Cell Open Wire result for Cell 12
0: No Open Wire on Cell 12
1: Open Wire on Cell 12";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL11 "Cell Open Wire result for Cell 11
0: No Open Wire on Cell 11
1: Open Wire on Cell 11";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL10 "Cell Open Wire result for Cell 10
0: No Open Wire on Cell 10
1: Open Wire on Cell 10";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL9 "Cell Open Wire result for Cell 9
0: No Open Wire on Cell 9
1: Open Wire on Cell 9";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL8 "Cell Open Wire result for Cell 8
0: No Open Wire on Cell 8
1: Open Wire on Cell 8";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL7 "Cell Open Wire result for Cell 7
0: No Open Wire on Cell 7
1: Open Wire on Cell 7";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL6 "Cell Open Wire result for Cell 6
0: No Open Wire on Cell 6
1: Open Wire on Cell 6";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL5 "Cell Open Wire result for Cell 5
0: No Open Wire on Cell 5
1: Open Wire on Cell 5";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL4 "Cell Open Wire result for Cell 4
0: No Open Wire on Cell 4
1: Open Wire on Cell 4";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL3 "Cell Open Wire result for Cell 3
0: No Open Wire on Cell 3
1: Open Wire on Cell 3";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL2 "Cell Open Wire result for Cell 2
0: No Open Wire on Cell 2
1: Open Wire on Cell 2";
CM_ SG_ 2415919525 BMS_02_CELL_OW_DATA_CELL1 "Cell Open Wire result for Cell 1
0: No Open Wire on Cell 1
1: Open Wire on Cell 1";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL16 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL15 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL14 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL13 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL12 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL11 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL10 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL9 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL8 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL7 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL6 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL5 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL4 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL3 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL2 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_CELL_RED_DATA_CELL1 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919525 BMS_02_BSW_FLT_IMPL_STATUS_FLT9 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919525 BMS_02_BSW_FLT_IMPL_STATUS_FLT8 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919525 BMS_02_BSW_FLT_IMPL_STATUS_FLT7 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919525 BMS_02_BSW_FLT_IMPL_STATUS_FLT6 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919525 BMS_02_BSW_FLT_IMPL_STATUS_FLT5 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919525 BMS_02_BSW_FLT_IMPL_STATUS_FLT4 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919525 BMS_02_BSW_FLT_IMPL_STATUS_FLT3 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919525 BMS_02_BSW_FLT_IMPL_STATUS_FLT2 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919525 BMS_02_BSW_FLT_IMPL_STATUS_FLT1 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919511 BMS_02_gpio_10 "GPIO Voltage";
CM_ SG_ 2415919511 BMS_02_gpio_9 "GPIO Voltage";
CM_ SG_ 2415919510 BMS_02_gpio_8 "GPIO Voltage";
CM_ SG_ 2415919510 BMS_02_gpio_7 "GPIO Voltage";
CM_ SG_ 2415919510 BMS_02_gpio_6 "GPIO Voltage";
CM_ SG_ 2415919510 BMS_02_gpio_5 "GPIO Voltage";
CM_ SG_ 2415919509 BMS_02_gpio_4 "GPIO Voltage";
CM_ SG_ 2415919509 BMS_02_gpio_3 "GPIO Voltage";
CM_ SG_ 2415919509 BMS_02_gpio_2 "GPIO Voltage";
CM_ SG_ 2415919509 BMS_02_gpio_1 "GPIO Voltage";
CM_ SG_ 2415919504 BMS_01_gpio_10 "GPIO Voltage";
CM_ SG_ 2415919504 BMS_01_gpio_9 "GPIO Voltage";
CM_ SG_ 2415919497 BMS_01_gpio_8 "GPIO Voltage";
CM_ SG_ 2415919497 BMS_01_gpio_7 "GPIO Voltage";
CM_ SG_ 2415919497 BMS_01_gpio_6 "GPIO Voltage";
CM_ SG_ 2415919497 BMS_01_gpio_5 "GPIO Voltage";
CM_ SG_ 2415919496 BMS_01_gpio_4 "GPIO Voltage";
CM_ SG_ 2415919496 BMS_01_gpio_3 "GPIO Voltage";
CM_ SG_ 2415919496 BMS_01_gpio_2 "GPIO Voltage";
CM_ SG_ 2415919496 BMS_01_gpio_1 "GPIO Voltage";
CM_ SG_ 2415919508 BMS_02_CellVolt_AVG13 "Raw Average Cell Voltage";
CM_ SG_ 2415919508 BMS_02_CellVolt_AVG14 "Raw Average Cell Voltage";
CM_ SG_ 2415919508 BMS_02_CellVolt_AVG15 "Raw Average Cell Voltage";
CM_ SG_ 2415919508 BMS_02_CellVolt_AVG16 "Raw Average Cell Voltage";
CM_ SG_ 2415919507 BMS_02_CellVolt_AVG09 "Raw Average Cell Voltage";
CM_ SG_ 2415919507 BMS_02_CellVolt_AVG10 "Raw Average Cell Voltage";
CM_ SG_ 2415919507 BMS_02_CellVolt_AVG11 "Raw Average Cell Voltage";
CM_ SG_ 2415919507 BMS_02_CellVolt_AVG12 "Raw Average Cell Voltage";
CM_ SG_ 2415919506 BMS_02_CellVolt_AVG05 "Raw Average Cell Voltage";
CM_ SG_ 2415919506 BMS_02_CellVolt_AVG06 "Raw Average Cell Voltage";
CM_ SG_ 2415919506 BMS_02_CellVolt_AVG07 "Raw Average Cell Voltage";
CM_ SG_ 2415919506 BMS_02_CellVolt_AVG08 "Raw Average Cell Voltage";
CM_ SG_ 2415919505 BMS_02_CellVolt_AVG01 "Raw Average Cell Voltage";
CM_ SG_ 2415919505 BMS_02_CellVolt_AVG02 "Raw Average Cell Voltage";
CM_ SG_ 2415919505 BMS_02_CellVolt_AVG03 "Raw Average Cell Voltage";
CM_ SG_ 2415919505 BMS_02_CellVolt_AVG04 "Raw Average Cell Voltage";
CM_ SG_ 2415919488 BMS_01_CellVolt_AVG01 "Raw Average Cell Voltage";
CM_ SG_ 2415919488 BMS_01_CellVolt_AVG02 "Raw Average Cell Voltage";
CM_ SG_ 2415919488 BMS_01_CellVolt_AVG03 "Raw Average Cell Voltage";
CM_ SG_ 2415919488 BMS_01_CellVolt_AVG04 "Raw Average Cell Voltage";
CM_ SG_ 2415919489 BMS_01_CellVolt_AVG05 "Raw Average Cell Voltage";
CM_ SG_ 2415919489 BMS_01_CellVolt_AVG06 "Raw Average Cell Voltage";
CM_ SG_ 2415919489 BMS_01_CellVolt_AVG07 "Raw Average Cell Voltage";
CM_ SG_ 2415919489 BMS_01_CellVolt_AVG08 "Raw Average Cell Voltage";
CM_ SG_ 2415919490 BMS_01_CellVolt_AVG09 "Raw Average Cell Voltage";
CM_ SG_ 2415919490 BMS_01_CellVolt_AVG10 "Raw Average Cell Voltage";
CM_ SG_ 2415919490 BMS_01_CellVolt_AVG11 "Raw Average Cell Voltage";
CM_ SG_ 2415919490 BMS_01_CellVolt_AVG12 "Raw Average Cell Voltage";
CM_ SG_ 2415919491 BMS_01_CellVolt_AVG13 "Raw Average Cell Voltage";
CM_ SG_ 2415919491 BMS_01_CellVolt_AVG14 "Raw Average Cell Voltage";
CM_ SG_ 2415919491 BMS_01_CellVolt_AVG15 "Raw Average Cell Voltage";
CM_ SG_ 2415919491 BMS_01_CellVolt_AVG16 "Raw Average Cell Voltage";
CM_ SG_ 2415919492 BMS_01_CC "Raw accumulated current";
CM_ SG_ 2415919492 BMS_01_CURRNT_AVG01 "Average current";
CM_ SG_ 2415919493 BMS_01_TB "Raw accumulated time base";
CM_ SG_ 2415919493 BMS_01_CELL_TEMPERATURE_1 "Raw GPIO voltage";
CM_ SG_ 2415919493 BMS_01_CELL_TEMPERATURE_2 "Raw GPIO voltage";
CM_ SG_ 2415919493 BMS_01_CELL_TEMPERATURE_3 "Raw GPIO voltage";
CM_ SG_ 2415919494 BMS_01_CELL_TEMPERATURE_4 "Raw GPIO voltage";
CM_ SG_ 2415919494 BMS_01_BOARD_TEMPERATURE "Raw GPIO voltage";
CM_ SG_ 2415919494 BATTERY_VOLTAGE "Raw GPIO voltage";
CM_ SG_ 2415919494 DC_LINK_VOLTAGE "Raw GPIO voltage";
CM_ SG_ 2415919495 BMS_01_CT_V "Free running C-ADC conversion counter.";
CM_ SG_ 2415919495 BMS_01_CTS_V "Free running C-ADC sub-sample conversion counter. 4 increments per sample.";
CM_ SG_ 2415919495 BMS_01_CT_I "Free running I-ADC conversion counter";
CM_ SG_ 2415919495 BMS_01_CTS_I "Free running I-ADC sub-sample conversion counter. 4 increments per sample.";
CM_ SG_ 2415919495 STATUS_FLAG1 "Provisinal flag bit";
CM_ SG_ 2415919495 STATUS_FLAG2 "Provisinal flag bit";
CM_ SG_ 2415919495 STATUS_FLAG3 "Provisinal flag bit";
CM_ SG_ 2415919495 STATUS_FLAG4 "Provisinal flag bit";
CM_ SG_ 2415919495 STATUS_FLAG5 "Provisinal flag bit";
CM_ SG_ 2415919495 STATUS_FLAG6 "Provisinal flag bit";
CM_ SG_ 2415919495 STATUS_FLAG7 "Provisinal flag bit";
CM_ SG_ 2415919495 STATUS_FLAG8 "Provisinal flag bit";
CM_ SG_ 2415919495 DIAGNOSTIC_FLAG1 "Provisinal flag bit";
CM_ SG_ 2415919495 DIAGNOSTIC_FLAG2 "Provisinal flag bit";
CM_ SG_ 2415919495 DIAGNOSTIC_FLAG3 "Provisinal flag bit";
CM_ SG_ 2415919495 DIAGNOSTIC_FLAG4 "Provisinal flag bit";
CM_ SG_ 2415919495 DIAGNOSTIC_FLAG5 "Provisinal flag bit";
CM_ SG_ 2415919495 DIAGNOSTIC_FLAG6 "Provisinal flag bit";
CM_ SG_ 2415919495 DIAGNOSTIC_FLAG7 "Provisinal flag bit";
CM_ SG_ 2415919495 DIAGNOSTIC_FLAG8 "Provisinal flag bit";
CM_ SG_ 2415919440 BMS_01_SOC_CELL1 "SOC Cell1";
CM_ SG_ 2415919440 BMS_01_SOC_CELL2 "SOC Cell2";
CM_ SG_ 2415919440 BMS_01_SOC_CELL3 "SOC Cell3";
CM_ SG_ 2415919440 BMS_01_SOC_CELL4 "SOC Cell4";
CM_ SG_ 2415919441 BMS_01_SOC_CELL5 "SOC Cell5";
CM_ SG_ 2415919441 BMS_01_SOC_CELL6 "SOC Cell6";
CM_ SG_ 2415919441 BMS_01_SOC_CELL7 "SOC Cell7";
CM_ SG_ 2415919441 BMS_01_SOC_CELL8 "SOC Cell8";
CM_ SG_ 2415919442 BMS_01_SOC_CELL9 "SOC Cell9";
CM_ SG_ 2415919442 BMS_01_SOC_CELL10 "SOC Cell10";
CM_ SG_ 2415919442 BMS_01_SOC_CELL11 "SOC Cell11";
CM_ SG_ 2415919442 BMS_01_SOC_CELL12 "SOC Cell12";
CM_ SG_ 2415919443 BMS_01_SOC_CELL13 "SOC Cell13";
CM_ SG_ 2415919443 BMS_01_SOC_CELL14 "SOC Cell14";
CM_ SG_ 2415919443 BMS_01_SOC_CELL15 "SOC Cell15";
CM_ SG_ 2415919443 BMS_01_SOC_CELL16 "SOC Cell16";
CM_ SG_ 2415919444 BMS_01_ERROR_SOC_CELL1 "Error in SOC Cell1";
CM_ SG_ 2415919444 BMS_01_ERROR_SOC_CELL2 "Error in SOC Cell2";
CM_ SG_ 2415919444 BMS_01_ERROR_SOC_CELL3 "Error in SOC Cell3";
CM_ SG_ 2415919444 BMS_01_ERROR_SOC_CELL4 "Error in SOC Cell4";
CM_ SG_ 2415919445 BMS_01_ERROR_SOC_CELL5 "Error in SOC Cell5";
CM_ SG_ 2415919445 BMS_01_ERROR_SOC_CELL6 "Error in SOC Cell6";
CM_ SG_ 2415919445 BMS_01_ERROR_SOC_CELL7 "Error in SOC Cell7";
CM_ SG_ 2415919445 BMS_01_ERROR_SOC_CELL8 "Error in SOC Cell8";
CM_ SG_ 2415919446 BMS_01_ERROR_SOC_CELL9 "Error in SOC Cell9";
CM_ SG_ 2415919446 BMS_01_ERROR_SOC_CELL10 "Error in SOC Cell10";
CM_ SG_ 2415919446 BMS_01_ERROR_SOC_CELL11 "Error in SOC Cell11";
CM_ SG_ 2415919446 BMS_01_ERROR_SOC_CELL12 "Error in SOC Cell12";
CM_ SG_ 2415919447 BMS_01_ERROR_SOC_CELL13 "Error in SOC Cell13";
CM_ SG_ 2415919447 BMS_01_ERROR_SOC_CELL14 "Error in SOC Cell14";
CM_ SG_ 2415919447 BMS_01_ERROR_SOC_CELL15 "Error in SOC Cell15";
CM_ SG_ 2415919447 BMS_01_ERROR_SOC_CELL16 "Error in SOC Cell16";
CM_ SG_ 2415919472 BMS_01_CHARGE_CURRENT "Charge current";
CM_ SG_ 2415919472 BMS_01_DISCHARGE_CURRENT "Discharge current";
CM_ SG_ 2415919472 BMS_01_CHARGE_POWER "Charge power";
CM_ SG_ 2415919472 BMS_01_DISHARGE_POWER "Discharge power";
CM_ SG_ 2415919473 deltat "deltat";
CM_ SG_ 2415919473 BMS_SOC_PACK "SOH";
CM_ SG_ 2415919473 BMS_01_SOH "SOH";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM1 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM2 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM5 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM7 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM9 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM10 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM14 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM15 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM17 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM19 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM21 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM23 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM24 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM26 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM28 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM29 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM30 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM31 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM33 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM35 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM38 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM39 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM40 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM41 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM42 "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_PASS_FAIL_LPF "SPF Pass/Fail status
0: SM Passed
1: SM Failed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM1 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM2 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM5 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM7 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM9 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM10 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM14 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM15 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM17 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM19 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM21 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM23 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM24 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM26 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM28 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM29 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM30 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM31 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM33 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM35 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM38 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM39 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM40 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM41 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_SM42 "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919520 BMS_01_SPF_EXEC_STATUS_LPF "SPF Execution status
0: SM not executed
1: SM executed";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM1 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM2 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM5 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM7 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM9 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM10 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM14 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM15 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM17 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM19 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM21 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM23 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM24 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM26 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM28 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM29 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM30 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM31 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM33 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM35 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM38 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM39 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM40 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM41 "SPF Implementation status
0: SM Not Implemented
1: SM Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM42 "Aux Open Wire test Implementation status
0: Test Not Implemented
1: Test Implemented";
CM_ SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_LPF "All LPF Implementation status
0: All LPFs Not Implemented
1: All LPFs Implemented";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT1 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT2 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT3 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT4 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT5 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT6 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT7 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT8 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT9 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT10 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT11 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT12 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT13 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT14 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT15 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT16 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT17 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT18 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT19 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919521 BMS_01_ASW_FLT_PASS_FAIL_FLT20 "ASW Fault status
0: FLT Passed
1: FLT Failed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT1 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT2 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT3 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT4 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT5 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT6 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT7 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT8 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT9 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT10 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT11 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT12 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT13 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT14 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT15 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT16 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT17 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT18 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT19 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT20 "ASW Fault Test Execution status
0: FLT not executed
1: FLT executed";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT1 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT2 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT3 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT4 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT5 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT6 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT7 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT8 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT9 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT10 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT11 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT12 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT13 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT14 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT15 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT16 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT17 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT18 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT19 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_ASW_FLT_IMPL_STATUS_FLT20 "ASW Fault Test Implementation status
0: FLT Not Implemented
1: FLT Implemented";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_PASS_FAIL_FLT1 "BSW Fault Status
0: FLT present
1: FLT not present";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_PASS_FAIL_FLT2 "BSW Fault Status
0: FLT present
1: FLT not present";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_PASS_FAIL_FLT3 "BSW Fault Status
0: FLT present
1: FLT not present";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_PASS_FAIL_FLT4 "BSW Fault Status
0: FLT present
1: FLT not present";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_PASS_FAIL_FLT5 "BSW Fault Status
0: FLT present
1: FLT not present";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_PASS_FAIL_FLT6 "BSW Fault Status
0: FLT present
1: FLT not present";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_PASS_FAIL_FLT7 "BSW Fault Status
0: FLT present
1: FLT not present";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_PASS_FAIL_FLT8 "BSW Fault Status
0: FLT present
1: FLT not present";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_PASS_FAIL_FLT9 "BSW Fault Status
0: FLT present
1: FLT not present";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_EXEC_STATUS_FLT1 "BSW Fault Test Execution Status
0: FLT test not executed
1: FLT test executed";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_EXEC_STATUS_FLT2 "BSW Fault Test Execution Status
0: FLT test not executed
1: FLT test executed";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_EXEC_STATUS_FLT3 "BSW Fault Test Execution Status
0: FLT test not executed
1: FLT test executed";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_EXEC_STATUS_FLT4 "BSW Fault Test Execution Status
0: FLT test not executed
1: FLT test executed";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_EXEC_STATUS_FLT5 "BSW Fault Test Execution Status
0: FLT test not executed
1: FLT test executed";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_EXEC_STATUS_FLT6 "BSW Fault Test Execution Status
0: FLT test not executed
1: FLT test executed";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_EXEC_STATUS_FLT7 "BSW Fault Test Execution Status
0: FLT test not executed
1: FLT test executed";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_EXEC_STATUS_FLT8 "BSW Fault Test Execution Status
0: FLT test not executed
1: FLT test executed";
CM_ SG_ 2415919522 BMS_01_BSW_FLT_EXEC_STATUS_FLT9 "BSW Fault Test Execution Status
0: FLT test not executed
1: FLT test executed";
CM_ SG_ 2415919523 BMS_01_BSW_FLT_IMPL_STATUS_FLT1 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919523 BMS_01_BSW_FLT_IMPL_STATUS_FLT2 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919523 BMS_01_BSW_FLT_IMPL_STATUS_FLT3 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919523 BMS_01_BSW_FLT_IMPL_STATUS_FLT4 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919523 BMS_01_BSW_FLT_IMPL_STATUS_FLT5 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919523 BMS_01_BSW_FLT_IMPL_STATUS_FLT6 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919523 BMS_01_BSW_FLT_IMPL_STATUS_FLT7 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919523 BMS_01_BSW_FLT_IMPL_STATUS_FLT8 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919523 BMS_01_BSW_FLT_IMPL_STATUS_FLT9 "BSW Fault Test Implementation Status
0: FLT test not implemented
1: FLT test implemented";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL1 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL2 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL3 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL4 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL5 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL6 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL7 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL8 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL9 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL10 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL11 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL12 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL13 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL14 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL15 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_RED_DATA_CELL16 "Cell Voltage Comparison with redundant ADC 
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL1 "Cell Open Wire result for Cell 1
0: No Open Wire on Cell 1
1: Open Wire on Cell 1";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL2 "Cell Open Wire result for Cell 2
0: No Open Wire on Cell 2
1: Open Wire on Cell 2";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL3 "Cell Open Wire result for Cell 3
0: No Open Wire on Cell 3
1: Open Wire on Cell 3";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL4 "Cell Open Wire result for Cell 4
0: No Open Wire on Cell 4
1: Open Wire on Cell 4";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL5 "Cell Open Wire result for Cell 5
0: No Open Wire on Cell 5
1: Open Wire on Cell 5";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL6 "Cell Open Wire result for Cell 6
0: No Open Wire on Cell 6
1: Open Wire on Cell 6";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL7 "Cell Open Wire result for Cell 7
0: No Open Wire on Cell 7
1: Open Wire on Cell 7";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL8 "Cell Open Wire result for Cell 8
0: No Open Wire on Cell 8
1: Open Wire on Cell 8";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL9 "Cell Open Wire result for Cell 9
0: No Open Wire on Cell 9
1: Open Wire on Cell 9";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL10 "Cell Open Wire result for Cell 10
0: No Open Wire on Cell 10
1: Open Wire on Cell 10";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL11 "Cell Open Wire result for Cell 11
0: No Open Wire on Cell 11
1: Open Wire on Cell 11";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL12 "Cell Open Wire result for Cell 12
0: No Open Wire on Cell 12
1: Open Wire on Cell 12";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL13 "Cell Open Wire result for Cell 13
0: No Open Wire on Cell 13
1: Open Wire on Cell 13";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL14 "Cell Open Wire result for Cell 14
0: No Open Wire on Cell 14
1: Open Wire on Cell 14";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL15 "Cell Open Wire result for Cell 15
0: No Open Wire on Cell 15
1: Open Wire on Cell 15";
CM_ SG_ 2415919523 BMS_01_CELL_OW_DATA_CELL16 "Cell Open Wire result for Cell 16
0: No Open Wire on Cell 16
1: Open Wire on Cell 16";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL1 "Cell Over voltage status for Cell 1
0: No overvoltage on Cell 1
1: Overvoltage on Cell 1";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL2 "Cell Over voltage status for Cell 2
0: No overvoltage on Cell 2
1: Overvoltage on Cell 2";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL3 "Cell Over voltage status for Cell 3
0: No overvoltage on Cell 3
1: Overvoltage on Cell 3";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL4 "Cell Over voltage status for Cell 4
0: No overvoltage on Cell 4
1: Overvoltage on Cell 4";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL5 "Cell Over voltage status for Cell 5
0: No overvoltage on Cell 5
1: Overvoltage on Cell 5";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL6 "Cell Over voltage status for Cell 6
0: No overvoltage on Cell 6
1: Overvoltage on Cell 6";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL7 "Cell Over voltage status for Cell 7
0: No overvoltage on Cell 7
1: Overvoltage on Cell 7";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL8 "Cell Over voltage status for Cell 8
0: No overvoltage on Cell 8
1: Overvoltage on Cell 8";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL9 "Cell Over voltage status for Cell 9
0: No overvoltage on Cell 9
1: Overvoltage on Cell 9";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL10 "Cell Over voltage status for Cell 10
0: No overvoltage on Cell 10
1: Overvoltage on Cell 10";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL11 "Cell Over voltage status for Cell 11
0: No overvoltage on Cell 11
1: Overvoltage on Cell 11";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL12 "Cell Over voltage status for Cell 12
0: No overvoltage on Cell 12
1: Overvoltage on Cell 12";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL13 "Cell Over voltage status for Cell 13
0: No overvoltage on Cell 13
1: Overvoltage on Cell 13";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL14 "Cell Over voltage status for Cell 14
0: No overvoltage on Cell 14
1: Overvoltage on Cell 14";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL15 "Cell Over voltage status for Cell 15
0: No overvoltage on Cell 15
1: Overvoltage on Cell 15";
CM_ SG_ 2415919523 BMS_01_CELL_OV_DATA_CELL16 "Cell Over voltage status for Cell 16
0: No overvoltage on Cell 16
1: Overvoltage on Cell 16";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL1 "Cell Under voltage status for Cell 1
0: No under voltage on Cell 1
1: Under voltage on Cell 1";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL2 "Cell Under voltage status for Cell 2
0: No under voltage on Cell 2
1: Under voltage on Cell 2";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL3 "Cell Under voltage status for Cell 3
0: No under voltage on Cell 3
1: Under voltage on Cell 3";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL4 "Cell Under voltage status for Cell 4
0: No under voltage on Cell 4
1: Under voltage on Cell 4";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL5 "Cell Under voltage status for Cell 5
0: No under voltage on Cell 5
1: Under voltage on Cell 5";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL6 "Cell Under voltage status for Cell 6
0: No under voltage on Cell 6
1: Under voltage on Cell 6";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL7 "Cell Under voltage status for Cell 7
0: No under voltage on Cell 7
1: Under voltage on Cell 7";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL8 "Cell Under voltage status for Cell 8
0: No under voltage on Cell 8
1: Under voltage on Cell 8";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL9 "Cell Under voltage status for Cell 9
0: No under voltage on Cell 9
1: Under voltage on Cell 9";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL10 "Cell Under voltage status for Cell 10
0: No under voltage on Cell 10
1: Under voltage on Cell 10";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL11 "Cell Under voltage status for Cell 11
0: No under voltage on Cell 11
1: Under voltage on Cell 11";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL12 "Cell Under voltage status for Cell 12
0: No under voltage on Cell 12
1: Under voltage on Cell 12";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL13 "Cell Under voltage status for Cell 13
0: No under voltage on Cell 13
1: Under voltage on Cell 13";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL14 "Cell Under voltage status for Cell 14
0: No under voltage on Cell 14
1: Under voltage on Cell 14";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL15 "Cell Under voltage status for Cell 15
0: No under voltage on Cell 15
1: Under voltage on Cell 15";
CM_ SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL16 "Cell Under voltage status for Cell 16
0: No under voltage on Cell 16
1: Under voltage on Cell 16";
CM_ SG_ 2415919524 BMS_01_GPIO_RED_DATA_GPIO1 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919524 BMS_01_GPIO_RED_DATA_GPIO2 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919524 BMS_01_GPIO_RED_DATA_GPIO3 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919524 BMS_01_GPIO_RED_DATA_GPIO4 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919524 BMS_01_GPIO_RED_DATA_GPIO5 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919524 BMS_01_GPIO_RED_DATA_GPIO6 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919524 BMS_01_GPIO_RED_DATA_GPIO7 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919524 BMS_01_GPIO_RED_DATA_GPIO8 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919524 BMS_01_GPIO_RED_DATA_GPIO9 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919524 BMS_01_GPIO_RED_DATA_GPIO10 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919524 BMS_01_GPIO_RED_DATA_GPIO11 "Redundant GPIO comparison status
0: Comparison passed
1: Comparison failed";
CM_ SG_ 2415919524 BMS_01_GPIO_OW_DATA_GPIO1 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919524 BMS_01_GPIO_OW_DATA_GPIO2 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919524 BMS_01_GPIO_OW_DATA_GPIO3 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919524 BMS_01_GPIO_OW_DATA_GPIO4 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919524 BMS_01_GPIO_OW_DATA_GPIO5 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919524 BMS_01_GPIO_OW_DATA_GPIO6 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919524 BMS_01_GPIO_OW_DATA_GPIO7 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919524 BMS_01_GPIO_OW_DATA_GPIO8 "GPIO Open Wire status
0: No Open Wire on GPIO
1: Open Wire on GPIO";
CM_ SG_ 2415919524 BMS_01_BAT_TEMP_OVUV_DATA_TOV1 "Battery Temperature Sensor Over voltage status
0: No Over Voltage
1: Over voltage";
CM_ SG_ 2415919524 BMS_01_BAT_TEMP_OVUV_DATA_TOV2 "Battery Temperature Sensor Over voltage status
0: No Over Voltage
1: Over voltage";
CM_ SG_ 2415919524 BMS_01_BAT_TEMP_OVUV_DATA_TOV3 "Battery Temperature Sensor Over voltage status
0: No Over Voltage
1: Over voltage";
CM_ SG_ 2415919524 BMS_01_BAT_TEMP_OVUV_DATA_TOV4 "Battery Temperature Sensor Over voltage status
0: No Over Voltage
1: Over voltage";
CM_ SG_ 2415919524 BMS_01_BAT_TEMP_OVUV_DATA_TUV1 "Battery Temperature Sensor Under voltage status
0: No Under Voltage
1: Under voltage";
CM_ SG_ 2415919524 BMS_01_BAT_TEMP_OVUV_DATA_TUV2 "Battery Temperature Sensor Under voltage status
0: No Under Voltage
1: Under voltage";
CM_ SG_ 2415919524 BMS_01_BAT_TEMP_OVUV_DATA_TUV3 "Battery Temperature Sensor Under voltage status
0: No Under Voltage
1: Under voltage";
CM_ SG_ 2415919524 BMS_01_BAT_TEMP_OVUV_DATA_TUV4 "Battery Temperature Sensor Under voltage status
0: No Under Voltage
1: Under voltage";
CM_ SG_ 2415919524 BMS_01_LPCM_FLT_DATA_CMF_GDVP "LPCM mode GPIO Delta Voltage in Positive Direction";
CM_ SG_ 2415919524 BMS_01_LPCM_FLT_DATA_CMF_GDVN "LPCM mode GPIO Delta Voltage in Negative Direction";
CM_ SG_ 2415919524 BMS_01_LPCM_FLT_DATA_CMF_GOV "LPCM mode Fault Monitoring Flag - GPIO Over Voltage";
CM_ SG_ 2415919524 BMS_01_LPCM_FLT_DATA_CMF_GUV "LPCM mode Fault Monitoring Flag - GPIO Under Voltage";
CM_ SG_ 2415919524 BMS_01_LPCM_FLT_DATA_CMF_CDVP "LPCM mode Cell Delta Voltage in Positive Direction";
CM_ SG_ 2415919524 BMS_01_LPCM_FLT_DATA_CMF_CDVN "LPCM mode Cell Delta Voltage in Negative Direction";
CM_ SG_ 2415919524 BMS_01_LPCM_FLT_DATA_CMF_COV "LPCM mode Fault Monitoring Flag - Cell Over Voltage";
CM_ SG_ 2415919524 BMS_01_LPCM_FLT_DATA_CMF_CUV "LPCM mode Fault Monitoring Flag - Cell Under Voltage";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM3 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM4 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM6 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM8 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM11 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM12 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM13 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM16 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM18 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM20 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM22 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM25 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM27 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM32 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM34 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM36 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM37 "LPF Pass/Fail status
0: LPF Passed
1: LPF Failed";
CM_ SG_ 2415919568 BMS_01_LPF_PASS_FAIL_COULCNTR "Coulomb counter diagnostic Pass/Fail status
0: Coulomb Counter Diagnostic Passed
1: Coulomb Counter Diagnostic Failed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM3 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM4 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM6 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM8 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM11 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM12 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM13 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM16 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM18 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM20 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM22 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM25 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM27 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM32 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM34 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM36 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_SM37 "LPF Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_EXEC_STATUS_COULCNTR "Coulomb Counter Diagnostic Execution status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM3 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM4 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM6 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM8 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM11 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM12 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM13 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM16 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM18 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM20 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM22 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM25 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM27 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM32 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM34 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM36 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_SM37 "LPF Implementation status
0: LPF executed
1: LPF not executed";
CM_ SG_ 2415919568 BMS_01_LPF_IMPL_STATUS_COULCNTR "Coulomb Counter Diagnostic Implementation status
0: Coulomb counter diagnostic executed
1: Coulomb counter diagnostic not executed";
CM_ SG_ ********** BMS_01_LATENT_CHK "Trigger latent check and others";
CM_ SG_ ********** BMS_01_VEHICLE_STATE "Vehicle state
0-> Wake-Up
1-> Sleep
2-> Parked
3-> Charging
4-> Driving
5-> Fault";
CM_ SG_ ********** BMS_01_DEBUG_MSG "Select the corresponding Debug message from the list. Provisional";
CM_ SG_ ********** BMS_01_VEHICLE_RESET "Set this bit to restart everything";
BA_DEF_ BO_  "GenMsgStartDelayTime" INT 0 0;
BA_DEF_ BO_  "GenMsgDelayTime" INT 0 0;
BA_DEF_ BO_  "GenMsgNrOfRepetition" INT 0 0;
BA_DEF_ BO_  "GenMsgCycleTimeFast" INT 0 0;
BA_DEF_ BO_  "GenMsgCycleTime" INT 0 0;
BA_DEF_ BO_  "GenMsgSendType" ENUM  "Cyclic","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed","IfActive","NoMsgSendType","NotUsed";
BA_DEF_ SG_  "GenSigStartValue" INT 0 0;
BA_DEF_ SG_  "GenSigInactiveValue" INT 0 0;
BA_DEF_ SG_  "GenSigCycleTimeActive" INT 0 0;
BA_DEF_ SG_  "GenSigCycleTime" INT 0 0;
BA_DEF_ SG_  "GenSigSendType" ENUM  "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed";
BA_DEF_  "Baudrate" INT 0 1000000;
BA_DEF_  "BusType" STRING ;
BA_DEF_  "NmType" STRING ;
BA_DEF_  "Manufacturer" STRING ;
BA_DEF_ BO_  "TpTxIndex" INT 0 255;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "NmStationAddress" HEX 0 255;
BA_DEF_ BU_  "NmNode" ENUM  "no","yes";
BA_DEF_ BO_  "NmMessage" ENUM  "no","yes";
BA_DEF_  "NmAsrWaitBusSleepTime" INT 0 65535;
BA_DEF_  "NmAsrTimeoutTime" INT 1 65535;
BA_DEF_  "NmAsrRepeatMessageTime" INT 0 65535;
BA_DEF_ BU_  "NmAsrNodeIdentifier" HEX 0 255;
BA_DEF_ BU_  "NmAsrNode" ENUM  "no","yes";
BA_DEF_  "NmAsrMessageCount" INT 1 256;
BA_DEF_ BO_  "NmAsrMessage" ENUM  "no","yes";
BA_DEF_ BU_  "NmAsrCanMsgReducedTime" INT 1 65535;
BA_DEF_  "NmAsrCanMsgCycleTime" INT 1 65535;
BA_DEF_ BU_  "NmAsrCanMsgCycleOffset" INT 0 65535;
BA_DEF_  "NmAsrBaseAddress" HEX 0 2047;
BA_DEF_ BU_  "ILUsed" ENUM  "no","yes";
BA_DEF_  "ILTxTimeout" INT 0 65535;
BA_DEF_ SG_  "GenSigTimeoutValue" INT 0 65535;
BA_DEF_ SG_  "GenSigTimeoutTime" INT 0 65535;
BA_DEF_ BO_  "GenMsgILSupport" ENUM  "no","yes";
BA_DEF_ BO_  "GenMsgFastOnStart" INT 0 65535;
BA_DEF_ BO_  "DiagUudtResponse" ENUM  "false","true";
BA_DEF_ BO_  "DiagUudResponse" ENUM  "False","True";
BA_DEF_ BO_  "DiagState" ENUM  "no","yes";
BA_DEF_ BO_  "DiagResponse" ENUM  "no","yes";
BA_DEF_ BO_  "DiagRequest" ENUM  "no","yes";
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_  "DBName" STRING ;
BA_DEF_DEF_  "GenMsgStartDelayTime" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "GenMsgSendType" "Cyclic";
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "GenSigCycleTimeActive" 0;
BA_DEF_DEF_  "GenSigCycleTime" 0;
BA_DEF_DEF_  "GenSigSendType" "Cyclic";
BA_DEF_DEF_  "Baudrate" 500000;
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "NmType" "";
BA_DEF_DEF_  "Manufacturer" "Vector";
BA_DEF_DEF_  "TpTxIndex" 0;
BA_DEF_DEF_  "NodeLayerModules" " ";
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "NmNode" "no";
BA_DEF_DEF_  "NmMessage" "no";
BA_DEF_DEF_  "NmAsrWaitBusSleepTime" 1500;
BA_DEF_DEF_  "NmAsrTimeoutTime" 2000;
BA_DEF_DEF_  "NmAsrRepeatMessageTime" 3200;
BA_DEF_DEF_  "NmAsrNodeIdentifier" 50;
BA_DEF_DEF_  "NmAsrNode" "no";
BA_DEF_DEF_  "NmAsrMessageCount" 128;
BA_DEF_DEF_  "NmAsrMessage" "no";
BA_DEF_DEF_  "NmAsrCanMsgReducedTime" 320;
BA_DEF_DEF_  "NmAsrCanMsgCycleTime" 640;
BA_DEF_DEF_  "NmAsrCanMsgCycleOffset" 0;
BA_DEF_DEF_  "NmAsrBaseAddress" 1280;
BA_DEF_DEF_  "ILUsed" "no";
BA_DEF_DEF_  "ILTxTimeout" 0;
BA_DEF_DEF_  "GenSigTimeoutValue" 0;
BA_DEF_DEF_  "GenSigTimeoutTime" 0;
BA_DEF_DEF_  "GenMsgILSupport" "no";
BA_DEF_DEF_  "GenMsgFastOnStart" 0;
BA_DEF_DEF_  "DiagUudtResponse" "false";
BA_DEF_DEF_  "DiagUudResponse" "False";
BA_DEF_DEF_  "DiagState" "no";
BA_DEF_DEF_  "DiagResponse" "no";
BA_DEF_DEF_  "DiagRequest" "no";
BA_DEF_DEF_  "VFrameFormat" "";
BA_DEF_DEF_  "DBName" "";
BA_ "Manufacturer" "Vector";
BA_ "NmType" "NmAsr";
BA_ "BusType" "CAN";
BA_ "Baudrate" 500000;
BA_ "NmAsrWaitBusSleepTime" 2000;
BA_ "DBName" "CAN_LightEV_Gen6_demo";
BA_ "GenMsgCycleTime" BO_ ********** 1;
BA_ "GenMsgSendType" BO_ ********** 0;
BA_ "VFrameFormat" BO_ ********** 15;
BA_ "GenMsgCycleTime" BO_ ********** 1;
BA_ "GenMsgSendType" BO_ ********** 0;
BA_ "VFrameFormat" BO_ ********** 15;
BA_ "GenMsgSendType" BO_ ********** 0;
BA_ "GenMsgCycleTime" BO_ ********** 0;
BA_ "VFrameFormat" BO_ ********** 15;
BA_ "GenMsgCycleTime" BO_ 2415919512 1;
BA_ "GenMsgSendType" BO_ 2415919512 0;
BA_ "VFrameFormat" BO_ 2415919512 15;
BA_ "GenMsgSendType" BO_ 2415919619 0;
BA_ "GenMsgCycleTime" BO_ 2415919619 1;
BA_ "VFrameFormat" BO_ 2415919619 15;
BA_ "GenMsgSendType" BO_ 2415919618 0;
BA_ "GenMsgCycleTime" BO_ 2415919618 1;
BA_ "VFrameFormat" BO_ 2415919618 15;
BA_ "GenMsgSendType" BO_ 2415919617 0;
BA_ "GenMsgCycleTime" BO_ 2415919617 1;
BA_ "VFrameFormat" BO_ 2415919617 15;
BA_ "GenMsgSendType" BO_ 2415919616 0;
BA_ "GenMsgCycleTime" BO_ 2415919616 1;
BA_ "VFrameFormat" BO_ 2415919616 15;
BA_ "GenMsgSendType" BO_ 2415919513 0;
BA_ "GenMsgCycleTime" BO_ 2415919513 1;
BA_ "VFrameFormat" BO_ 2415919513 15;
BA_ "GenMsgCycleTime" BO_ 2415951876 1;
BA_ "GenMsgSendType" BO_ 2415951876 0;
BA_ "VFrameFormat" BO_ 2415951876 15;
BA_ "GenMsgCycleTime" BO_ 2415951875 1;
BA_ "GenMsgSendType" BO_ 2415951875 0;
BA_ "VFrameFormat" BO_ 2415951875 15;
BA_ "GenMsgCycleTime" BO_ 2415951874 1;
BA_ "GenMsgSendType" BO_ 2415951874 0;
BA_ "VFrameFormat" BO_ 2415951874 15;
BA_ "GenMsgCycleTime" BO_ 2415951873 1;
BA_ "GenMsgSendType" BO_ 2415951873 0;
BA_ "VFrameFormat" BO_ 2415951873 15;
BA_ "GenMsgSendType" BO_ 2415919526 0;
BA_ "GenMsgCycleTime" BO_ 2415919526 0;
BA_ "VFrameFormat" BO_ 2415919526 15;
BA_ "GenMsgSendType" BO_ 2415919525 0;
BA_ "GenMsgCycleTime" BO_ 2415919525 0;
BA_ "VFrameFormat" BO_ 2415919525 15;
BA_ "GenMsgSendType" BO_ 2415919511 0;
BA_ "GenMsgCycleTime" BO_ 2415919511 1;
BA_ "VFrameFormat" BO_ 2415919511 15;
BA_ "GenMsgSendType" BO_ 2415919510 0;
BA_ "GenMsgCycleTime" BO_ 2415919510 1;
BA_ "VFrameFormat" BO_ 2415919510 15;
BA_ "GenMsgSendType" BO_ 2415919509 0;
BA_ "GenMsgCycleTime" BO_ 2415919509 1;
BA_ "VFrameFormat" BO_ 2415919509 15;
BA_ "GenMsgSendType" BO_ 2415919504 0;
BA_ "GenMsgCycleTime" BO_ 2415919504 1;
BA_ "VFrameFormat" BO_ 2415919504 15;
BA_ "GenMsgSendType" BO_ 2415919497 0;
BA_ "GenMsgCycleTime" BO_ 2415919497 1;
BA_ "VFrameFormat" BO_ 2415919497 15;
BA_ "GenMsgCycleTime" BO_ 2415919496 1;
BA_ "GenMsgSendType" BO_ 2415919496 0;
BA_ "VFrameFormat" BO_ 2415919496 15;
BA_ "GenMsgSendType" BO_ 2415919508 0;
BA_ "GenMsgCycleTime" BO_ 2415919508 1;
BA_ "VFrameFormat" BO_ 2415919508 15;
BA_ "GenMsgSendType" BO_ 2415919507 0;
BA_ "GenMsgCycleTime" BO_ 2415919507 1;
BA_ "VFrameFormat" BO_ 2415919507 15;
BA_ "GenMsgSendType" BO_ 2415919506 0;
BA_ "GenMsgCycleTime" BO_ 2415919506 1;
BA_ "VFrameFormat" BO_ 2415919506 15;
BA_ "GenMsgCycleTime" BO_ 2415919505 1;
BA_ "GenMsgSendType" BO_ 2415919505 0;
BA_ "VFrameFormat" BO_ 2415919505 15;
BA_ "GenMsgSendType" BO_ 2415919488 0;
BA_ "GenMsgCycleTime" BO_ 2415919488 1;
BA_ "VFrameFormat" BO_ 2415919488 15;
BA_ "GenMsgSendType" BO_ 2415919489 0;
BA_ "GenMsgCycleTime" BO_ 2415919489 1;
BA_ "VFrameFormat" BO_ 2415919489 15;
BA_ "GenMsgSendType" BO_ 2415919490 0;
BA_ "GenMsgCycleTime" BO_ 2415919490 1;
BA_ "VFrameFormat" BO_ 2415919490 15;
BA_ "GenMsgSendType" BO_ 2415919491 0;
BA_ "GenMsgCycleTime" BO_ 2415919491 1;
BA_ "VFrameFormat" BO_ 2415919491 15;
BA_ "GenMsgSendType" BO_ 2415919492 0;
BA_ "GenMsgCycleTime" BO_ 2415919492 1;
BA_ "VFrameFormat" BO_ 2415919492 15;
BA_ "GenMsgSendType" BO_ 2415919493 0;
BA_ "GenMsgCycleTime" BO_ 2415919493 1;
BA_ "VFrameFormat" BO_ 2415919493 15;
BA_ "GenMsgSendType" BO_ 2415919494 0;
BA_ "GenMsgCycleTime" BO_ 2415919494 1;
BA_ "VFrameFormat" BO_ 2415919494 15;
BA_ "GenMsgSendType" BO_ 2415919495 0;
BA_ "GenMsgCycleTime" BO_ 2415919495 1;
BA_ "VFrameFormat" BO_ 2415919495 15;
BA_ "GenMsgSendType" BO_ 2415919440 0;
BA_ "GenMsgCycleTime" BO_ 2415919440 0;
BA_ "VFrameFormat" BO_ 2415919440 15;
BA_ "GenMsgSendType" BO_ 2415919441 0;
BA_ "GenMsgCycleTime" BO_ 2415919441 0;
BA_ "VFrameFormat" BO_ 2415919441 15;
BA_ "GenMsgSendType" BO_ 2415919442 0;
BA_ "GenMsgCycleTime" BO_ 2415919442 0;
BA_ "VFrameFormat" BO_ 2415919442 15;
BA_ "GenMsgSendType" BO_ 2415919443 0;
BA_ "GenMsgCycleTime" BO_ 2415919443 0;
BA_ "VFrameFormat" BO_ 2415919443 15;
BA_ "GenMsgSendType" BO_ 2415919444 0;
BA_ "GenMsgCycleTime" BO_ 2415919444 0;
BA_ "VFrameFormat" BO_ 2415919444 15;
BA_ "GenMsgSendType" BO_ 2415919445 0;
BA_ "GenMsgCycleTime" BO_ 2415919445 0;
BA_ "VFrameFormat" BO_ 2415919445 15;
BA_ "GenMsgSendType" BO_ 2415919446 0;
BA_ "GenMsgCycleTime" BO_ 2415919446 0;
BA_ "VFrameFormat" BO_ 2415919446 15;
BA_ "GenMsgSendType" BO_ 2415919447 0;
BA_ "GenMsgCycleTime" BO_ 2415919447 0;
BA_ "VFrameFormat" BO_ 2415919447 15;
BA_ "GenMsgSendType" BO_ 2415919472 0;
BA_ "GenMsgCycleTime" BO_ 2415919472 0;
BA_ "VFrameFormat" BO_ 2415919472 15;
BA_ "GenMsgSendType" BO_ 2415919473 0;
BA_ "GenMsgCycleTime" BO_ 2415919473 0;
BA_ "VFrameFormat" BO_ 2415919473 15;
BA_ "GenMsgSendType" BO_ 2415919520 0;
BA_ "GenMsgCycleTime" BO_ 2415919520 0;
BA_ "VFrameFormat" BO_ 2415919520 15;
BA_ "GenMsgSendType" BO_ 2415919521 0;
BA_ "GenMsgCycleTime" BO_ 2415919521 0;
BA_ "VFrameFormat" BO_ 2415919521 15;
BA_ "GenMsgSendType" BO_ 2415919522 0;
BA_ "GenMsgCycleTime" BO_ 2415919522 0;
BA_ "VFrameFormat" BO_ 2415919522 15;
BA_ "GenMsgSendType" BO_ 2415919523 0;
BA_ "GenMsgCycleTime" BO_ 2415919523 0;
BA_ "VFrameFormat" BO_ 2415919523 15;
BA_ "GenMsgSendType" BO_ 2415919524 0;
BA_ "GenMsgCycleTime" BO_ 2415919524 0;
BA_ "VFrameFormat" BO_ 2415919524 15;
BA_ "GenMsgSendType" BO_ 2415919568 0;
BA_ "GenMsgCycleTime" BO_ 2415919568 0;
BA_ "VFrameFormat" BO_ 2415919568 15;
BA_ "GenMsgSendType" BO_ ********** 0;
BA_ "GenMsgCycleTime" BO_ ********** 0;
BA_ "VFrameFormat" BO_ ********** 15;
BA_ "GenSigStartValue" SG_ ********** BMS_02_TB 0;
BA_ "GenSigStartValue" SG_ ********** BMS_02_BATTERY_VOLTAGE 3750;
BA_ "GenSigStartValue" SG_ ********** BMS_02_CELL_TEMPERATURE_4 0;
BA_ "GenSigStartValue" SG_ 2415919512 current_2 0;
BA_ "GenSigStartValue" SG_ 2415919512 current_1 0;
BA_ "GenSigStartValue" SG_ 2415919619 diag_result_deltaT 0;
BA_ "GenSigStartValue" SG_ 2415919619 stat_voltage_deltaT 0;
BA_ "GenSigStartValue" SG_ 2415919619 gpio_data_deltaT 0;
BA_ "GenSigStartValue" SG_ 2415919619 cell_voltage_deltaT 0;
BA_ "GenSigStartValue" SG_ 2415919618 Current_CT -0;
BA_ "GenSigStartValue" SG_ 2415919618 Voltage_CT -0;
BA_ "GenSigStartValue" SG_ 2415919513 time_base 0;
BA_ "GenSigStartValue" SG_ 2415919513 coulomb_counting 0;
BA_ "GenSigStartValue" SG_ 2415951876 Battery_MinCharging_Current 5;
BA_ "GenSigStartValue" SG_ 2415951875 Battery_Discharging_Efficiency 98;
BA_ "GenSigStartValue" SG_ 2415951875 Battery_MaxCharging_Voltage 78;
BA_ "GenSigStartValue" SG_ 2415951874 Battery_Charging_Efficiency 98;
BA_ "GenSigStartValue" SG_ 2415951874 Battery_DischargeCutOff_Voltage 59;
BA_ "GenSigStartValue" SG_ 2415951873 Battery_Initial_SOC 60;
BA_ "GenSigStartValue" SG_ 2415951873 Battery_NominalRated_Capacity 100;
BA_ "GenSigStartValue" SG_ 2415919526 BMS_02_CELL_UV_DATA_CELL1 0;
BA_ "GenSigStartValue" SG_ 2415919525 BMS_02_BSW_FLT_IMPL_STATUS_FLT1 0;
BA_ "GenSigStartValue" SG_ 2415919511 BMS_02_gpio_10 0;
BA_ "GenSigStartValue" SG_ 2415919511 BMS_02_gpio_9 0;
BA_ "GenSigStartValue" SG_ 2415919510 BMS_02_gpio_8 0;
BA_ "GenSigStartValue" SG_ 2415919510 BMS_02_gpio_7 0;
BA_ "GenSigStartValue" SG_ 2415919510 BMS_02_gpio_6 0;
BA_ "GenSigStartValue" SG_ 2415919510 BMS_02_gpio_5 0;
BA_ "GenSigStartValue" SG_ 2415919509 BMS_02_gpio_4 0;
BA_ "GenSigStartValue" SG_ 2415919509 BMS_02_gpio_3 0;
BA_ "GenSigStartValue" SG_ 2415919509 BMS_02_gpio_2 0;
BA_ "GenSigStartValue" SG_ 2415919509 BMS_02_gpio_1 0;
BA_ "GenSigStartValue" SG_ 2415919504 BMS_01_gpio_10 0;
BA_ "GenSigStartValue" SG_ 2415919504 BMS_01_gpio_9 0;
BA_ "GenSigStartValue" SG_ 2415919497 BMS_01_gpio_8 0;
BA_ "GenSigStartValue" SG_ 2415919497 BMS_01_gpio_7 0;
BA_ "GenSigStartValue" SG_ 2415919497 BMS_01_gpio_6 0;
BA_ "GenSigStartValue" SG_ 2415919497 BMS_01_gpio_5 0;
BA_ "GenSigStartValue" SG_ 2415919496 BMS_01_gpio_4 0;
BA_ "GenSigStartValue" SG_ 2415919496 BMS_01_gpio_3 0;
BA_ "GenSigStartValue" SG_ 2415919496 BMS_01_gpio_2 0;
BA_ "GenSigStartValue" SG_ 2415919496 BMS_01_gpio_1 0;
BA_ "GenSigStartValue" SG_ 2415919508 BMS_02_CellVolt_AVG13 -12768;
BA_ "GenSigStartValue" SG_ 2415919507 BMS_02_CellVolt_AVG09 -12768;
BA_ "GenSigStartValue" SG_ 2415919506 BMS_02_CellVolt_AVG05 -12768;
BA_ "GenSigStartValue" SG_ 2415919505 BMS_02_CellVolt_AVG01 -12768;
BA_ "GenSigStartValue" SG_ 2415919489 BMS_01_CellVolt_AVG05 -12768;
BA_ "GenSigStartValue" SG_ 2415919490 BMS_01_CellVolt_AVG09 -12768;
BA_ "GenSigStartValue" SG_ 2415919491 BMS_01_CellVolt_AVG13 -12768;
BA_ "GenSigStartValue" SG_ 2415919492 BMS_01_CURRNT_AVG01 -1500;
BA_ "GenSigStartValue" SG_ 2415919493 BMS_01_TB 0;
BA_ "GenSigStartValue" SG_ 2415919494 BMS_01_CELL_TEMPERATURE_4 0;
BA_ "GenSigStartValue" SG_ 2415919494 BATTERY_VOLTAGE 3750;
BA_ "GenSigStartValue" SG_ 2415919495 BMS_01_CT_V 0;
BA_ "GenSigStartValue" SG_ 2415919440 BMS_01_SOC_CELL1 0;
BA_ "GenSigStartValue" SG_ 2415919441 BMS_01_SOC_CELL5 0;
BA_ "GenSigStartValue" SG_ 2415919442 BMS_01_SOC_CELL9 0;
BA_ "GenSigStartValue" SG_ 2415919443 BMS_01_SOC_CELL13 0;
BA_ "GenSigStartValue" SG_ 2415919444 BMS_01_ERROR_SOC_CELL1 0;
BA_ "GenSigStartValue" SG_ 2415919445 BMS_01_ERROR_SOC_CELL5 0;
BA_ "GenSigStartValue" SG_ 2415919446 BMS_01_ERROR_SOC_CELL9 0;
BA_ "GenSigStartValue" SG_ 2415919447 BMS_01_ERROR_SOC_CELL13 0;
BA_ "GenSigStartValue" SG_ 2415919472 BMS_01_CHARGE_CURRENT -0;
BA_ "GenSigStartValue" SG_ 2415919473 BMS_SOC_PACK 0;
BA_ "GenSigStartValue" SG_ 2415919473 BMS_01_SOH 0;
BA_ "GenSigStartValue" SG_ 2415919520 BMS_01_SPF_PASS_FAIL_SM1 1;
BA_ "GenSigStartValue" SG_ 2415919521 BMS_01_SPF_IMPL_STATUS_SM1 0;
BA_ "GenSigStartValue" SG_ 2415919522 BMS_01_ASW_FLT_EXEC_STATUS_FLT1 0;
BA_ "GenSigStartValue" SG_ 2415919523 BMS_01_BSW_FLT_IMPL_STATUS_FLT1 0;
BA_ "GenSigStartValue" SG_ 2415919524 BMS_01_CELL_UV_DATA_CELL1 0;
BA_ "GenSigStartValue" SG_ 2415919568 BMS_01_LPF_PASS_FAIL_SM3 1;
BA_ "GenSigStartValue" SG_ ********** BMS_01_LATENT_CHK 1;
VAL_ ********** BMS_02_DC_LINK_VOLTAGE 10088 "Init" ;
VAL_ ********** BMS_02_BATTERY_VOLTAGE 10088 "Init" ;
VAL_ ********** BMS_02_BOARD_TEMPERATURE 10088 "Init" ;
VAL_ ********** BMS_02_CELL_TEMPERATURE_4 10088 "Init" ;
VAL_ ********** BMS_02_CELL_TEMPERATURE_3 10088 "Init" ;
VAL_ ********** BMS_02_CELL_TEMPERATURE_2 10088 "Init" ;
VAL_ ********** BMS_02_CELL_TEMPERATURE_1 10088 "Init" ;
VAL_ 2415919617 stat_8 10088 "Init" ;
VAL_ 2415919617 stat_7 10088 "Init" ;
VAL_ 2415919617 stat_6 10088 "Init" ;
VAL_ 2415919617 stat_5 10088 "Init" ;
VAL_ 2415919616 stat_4 10088 "Init" ;
VAL_ 2415919616 stat_3 10088 "Init" ;
VAL_ 2415919616 stat_1 10088 "Init" ;
VAL_ 2415919511 BMS_02_gpio_10 10088 "Init" ;
VAL_ 2415919511 BMS_02_gpio_9 10088 "Init" ;
VAL_ 2415919510 BMS_02_gpio_8 10088 "Init" ;
VAL_ 2415919510 BMS_02_gpio_7 10088 "Init" ;
VAL_ 2415919510 BMS_02_gpio_6 10088 "Init" ;
VAL_ 2415919510 BMS_02_gpio_5 10088 "Init" ;
VAL_ 2415919509 BMS_02_gpio_4 10088 "Init" ;
VAL_ 2415919509 BMS_02_gpio_3 10088 "Init" ;
VAL_ 2415919509 BMS_02_gpio_2 10088 "Init" ;
VAL_ 2415919509 BMS_02_gpio_1 10088 "Init" ;
VAL_ 2415919504 BMS_01_gpio_10 10088 "Init" ;
VAL_ 2415919504 BMS_01_gpio_9 10088 "Init" ;
VAL_ 2415919497 BMS_01_gpio_8 10088 "Init" ;
VAL_ 2415919497 BMS_01_gpio_7 10088 "Init" ;
VAL_ 2415919497 BMS_01_gpio_6 10088 "Init" ;
VAL_ 2415919497 BMS_01_gpio_5 10088 "Init" ;
VAL_ 2415919496 BMS_01_gpio_4 10088 "Init" ;
VAL_ 2415919496 BMS_01_gpio_3 10088 "Init" ;
VAL_ 2415919496 BMS_01_gpio_2 10088 "Init" ;
VAL_ 2415919496 BMS_01_gpio_1 10088 "Init" ;
VAL_ 2415919508 BMS_02_CellVolt_AVG13 10088 "Init" ;
VAL_ 2415919508 BMS_02_CellVolt_AVG14 10088 "Init" ;
VAL_ 2415919508 BMS_02_CellVolt_AVG15 10088 "Init" ;
VAL_ 2415919508 BMS_02_CellVolt_AVG16 10088 "Init" ;
VAL_ 2415919507 BMS_02_CellVolt_AVG09 10088 "Init" ;
VAL_ 2415919507 BMS_02_CellVolt_AVG10 10088 "Init" ;
VAL_ 2415919507 BMS_02_CellVolt_AVG11 10088 "Init" ;
VAL_ 2415919507 BMS_02_CellVolt_AVG12 10088 "Init" ;
VAL_ 2415919506 BMS_02_CellVolt_AVG05 10088 "Init" ;
VAL_ 2415919506 BMS_02_CellVolt_AVG06 10088 "Init" ;
VAL_ 2415919506 BMS_02_CellVolt_AVG07 10088 "Init" ;
VAL_ 2415919506 BMS_02_CellVolt_AVG08 10088 "Init" ;
VAL_ 2415919505 BMS_02_CellVolt_AVG01 10088 "Init" ;
VAL_ 2415919505 BMS_02_CellVolt_AVG02 10088 "Init" ;
VAL_ 2415919505 BMS_02_CellVolt_AVG03 10088 "Init" ;
VAL_ 2415919505 BMS_02_CellVolt_AVG04 10088 "Init" ;
VAL_ 2415919488 BMS_01_CellVolt_AVG01 10088 "Init" ;
VAL_ 2415919488 BMS_01_CellVolt_AVG02 10088 "Init" ;
VAL_ 2415919488 BMS_01_CellVolt_AVG03 10088 "Init" ;
VAL_ 2415919488 BMS_01_CellVolt_AVG04 10088 "Init" ;
VAL_ 2415919489 BMS_01_CellVolt_AVG05 10088 "Init" ;
VAL_ 2415919489 BMS_01_CellVolt_AVG06 10088 "Init" ;
VAL_ 2415919489 BMS_01_CellVolt_AVG07 10088 "Init" ;
VAL_ 2415919489 BMS_01_CellVolt_AVG08 10088 "Init" ;
VAL_ 2415919490 BMS_01_CellVolt_AVG09 10088 "Init" ;
VAL_ 2415919490 BMS_01_CellVolt_AVG10 10088 "Init" ;
VAL_ 2415919490 BMS_01_CellVolt_AVG11 10088 "Init" ;
VAL_ 2415919490 BMS_01_CellVolt_AVG12 10088 "Init" ;
VAL_ 2415919491 BMS_01_CellVolt_AVG13 10088 "Init" ;
VAL_ 2415919491 BMS_01_CellVolt_AVG14 10088 "Init" ;
VAL_ 2415919491 BMS_01_CellVolt_AVG15 10088 "Init" ;
VAL_ 2415919491 BMS_01_CellVolt_AVG16 10088 "Init" ;
VAL_ 2415919493 BMS_01_CELL_TEMPERATURE_1 10088 "Init" ;
VAL_ 2415919493 BMS_01_CELL_TEMPERATURE_2 10088 "Init" ;
VAL_ 2415919493 BMS_01_CELL_TEMPERATURE_3 10088 "Init" ;
VAL_ 2415919494 BMS_01_CELL_TEMPERATURE_4 10088 "Init" ;
VAL_ 2415919494 BMS_01_BOARD_TEMPERATURE 10088 "Init" ;
VAL_ 2415919494 BATTERY_VOLTAGE 10088 "Init" ;
VAL_ 2415919494 DC_LINK_VOLTAGE 10088 "Init" ;

