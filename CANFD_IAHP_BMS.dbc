VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: BMS HOST

VAL_TABLE_ VT_BMS_BatteryStatusLow 15 "ST" 14 "RUN" 13 "COMM_PRESENTF" 12 "KEY_PRESENTF1" 11 "KEY_PRESENTF0" 10 "CHG_PRESENTF1" 9 "CHG_PRESENTF0" 8 "LTCF" 7 "LTUF" 6 "WARRANTY" 5 "BEF" 4 "BFF" 3 "CBF" 2 "DSMF" 1 "CHGF" 0 "ZEROF" ;
VAL_TABLE_ VT_BMS_BatteryStatusHigh 15 "VSTG1" 14 "VSTG0" 13 "ZERO_DSG" 12 "SC_RST" 11 "BMS_PST_F" 10 "BMS_11VCC_F" 9 "DCHG_OFF_F" 8 "WH_OCV_F" 7 "BMS_RESET_F" 6 "BMS_FAULT_F" 5 "CHG_EN_F" 4 "DCHG_EN_F" 3 "SC_RELEASE_F" 2 "CHG_MODE" 1 "BMS_PRESENT_F" 0 "Reserved" ;
VAL_TABLE_ VT_BMS_PackStatusLow 15 "FDM_EN" 14 "DFCP" 13 "CFCP" 12 "LVDR" 11 "PRE-DSGMS" 10 "PRE-CHGMS" 9 "DFDP" 8 "CFDP" 7 "FCCFlagUpdate" 6 "EventFlag" 5 "WDTRST" 4 "DFBS" 3 "DFAS" 2 "SEALDFB" 1 "SEALDFA" 0 "PrepUpdateFCC" ;
VAL_TABLE_ VT_BMS_PackStatusHigh 15 "Reserved" 14 "SOCB" 13 "SMCB" 12 "LT_OCV_prd" 11 "UNPLG_SD" 10 "DFDS" 9 "AccCHG" 8 "LT_OCV_EN" 7 "FLT_INJ" 6 "Data_UT" 5 "RST_CD" 4 "XDFCP" 3 "XCFCP" 2 "XDFDP" 1 "XCFDP" 0 "Pack_Idle" ;
VAL_TABLE_ VT_BMS_SafetyStatusLow 15 "SD_Fail" 14 "AFE_COMM" 13 "MOSOTUTF" 12 "Reserved" 11 "11V_OVP" 10 "Reserved" 9 "COCF" 8 "DOCF" 7 "Reserved" 6 "DCI" 5 "DUTF" 4 "DOTF" 3 "CUTF" 2 "COTF" 1 "UVPF" 0 "OVPF" ;
VAL_TABLE_ VT_BMS_SafetyStatusHigh 15 "FLT_INJ_PF" 14 "FDM" 13 "Reserved" 12 "AFEDOC" 11 "DOPP" 10 "DUP" 9 "Reserved" 8 "FETNG_ExtSCP" 7 "FETNG_FETOFF" 6 "FETNG_ALM1" 5 "DOC3" 4 "DOC2" 3 "IRAF" 2 "PDSGF" 1 "Reserved" 0 "HW_DOCF" ;
VAL_TABLE_ VT_BMS_WarnStatus 15 "Reserved" 14 "COTAF" 13 "MOSOTUTF" 12 "Reserved" 11 "Reserved" 10 "Reserved" 9 "COCF" 8 "DOCF" 7 "Reserved" 6 "Reserved" 5 "DUTF" 4 "DOTF" 3 "CUTF" 2 "COTF" 1 "UVPF" 0 "OVPF" ;
VAL_TABLE_ VT_BMS_PFStatusLow 15 "LBCF" 14 "FET_NG" 13 "Reserved" 12 "OTPF" 11 "Reserved" 10 "CFUSE" 9 "AFE_NG" 8 "FETF2" 7 "AFE_PATAM" 6 "UVPF" 5 "MCUF" 4 "FETF" 3 "P2ND" 2 "OVPF" 1 "FUSE" 0 "CUBF" ;
VAL_TABLE_ VT_BMS_CBS0_15 15 "Cell-16" 14 "Cell-15" 13 "Cell-14" 12 "Cell-13" 11 "Cell-12" 10 "Cell-11" 9 "Cell-10" 8 "Cell-9" 7 "Cell-8" 6 "Cell-7" 5 "Cell-6" 4 "Cell-5" 3 "Cell-4" 2 "Cell-3" 1 "Cell-2" 0 "Cell-1" ;
VAL_TABLE_ VT_BMS_STStatus 15 "Reserved" 14 "MCU_AD" 13 "AADC_T" 12 "INT" 11 "AOSC" 10 "AADC_C" 9 "OVD" 8 "FET" 7 "AADC_V" 6 "OSC" 5 "IDP" 4 "IOP" 3 "ROM" 2 "STA" 1 "RAM" 0 "CPU" ;
VAL_TABLE_ VT_BMS_PFStatusHigh 15 "Bit15" 14 "Bit14" 13 "Bit13" 12 "Bit12" 11 "Bit11" 10 "Bit10" 9 "Bit9" 8 "Bit8" 7 "Bit7" 6 "Bit6" 5 "Bit5" 4 "Bit4" 3 "Bit3" 2 "Bit2" 1 "Bit1" 0 "Bit0" ;
VAL_TABLE_ VT_BMS_EngMode 7 "Bit7" 6 "Bit6" 5 "Bit5" 4 "Bit4" 3 "Bit3" 2 "Bit2" 1 "Bit1" 0 "Bit0" ;

BO_ 2147485568 BMS_PackInfo: 64 BMS
 SG_ BMS_Vpack : 0|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_PackVoltage : 32|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_PackCurrent : 64|32@1- (1,0) [-2147483648|2147483647] "mA"  HOST
 SG_ BMS_AvgCurrent : 96|32@1- (1,0) [-2147483648|2147483647] "mA"  HOST
 SG_ BMS_RSOC : 128|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_ASOC : 144|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_RC : 160|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_FCC : 192|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_CycleCount : 224|16@1+ (1,0) [0|65535] "Times"  HOST
 SG_ BMS_LearnCycle : 240|16@1+ (1,0) [0|65535] "Times"  HOST
 SG_ BMS_UserRC : 256|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_DCR : 288|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_FDCR : 320|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_UserRSOC : 352|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_FCCmin : 368|32@1+ (1,0) [0|4294967295] "100mWh"  HOST
 SG_ BMS_DeltaRC : 400|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_SrartRSOC : 432|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_StartFDCR : 448|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_RCminCutoff : 480|32@1+ (1,0) [0|4294967295] "mAh"  HOST

BO_ 2147485569 BMS_Status: 64 BMS
 SG_ BMS_BatteryStatusLow : 0|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_BatteryStatusHigh : 16|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_PackStatusLow : 32|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_PackStatusHigh : 48|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_SafetyStatusLow : 64|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_SafetyStatusHigh : 80|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_WarnStatus : 96|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_STStatus : 112|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_PFStatusLow : 128|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_PFStatusHigh : 144|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_CBS0_15 : 160|16@1+ (1,0) [0|65535] "Hex"  HOST
 SG_ BMS_StartRSOCmin : 176|16@1+ (0.01,0) [0|655.35] "%"  HOST
 SG_ BMS_UsageCapacity : 192|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_SuccChaCap : 224|32@1+ (1,0) [0|4294967295] "mAh"  HOST
 SG_ BMS_SystemTime : 256|32@1+ (1,0) [0|4294967295] "DateTime"  HOST
 SG_ BMS_EngMode : 288|8@1+ (1,0) [0|255] "Hex"  HOST

BO_ 2147485570 BMS_Temperatures1: 64 BMS
 SG_ BMS_Temp1 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp2 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp3 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp4 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp5 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp6 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp7 : 96|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp8 : 112|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp9 : 128|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp10 : 144|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp11 : 160|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp12 : 176|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp13 : 192|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp14 : 208|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp15 : 224|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp16 : 240|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp17 : 256|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp18 : 272|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp19 : 288|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp20 : 304|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp21 : 320|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp22 : 336|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp23 : 352|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp24 : 368|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp25 : 384|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp26 : 400|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp27 : 416|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp28 : 432|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp29 : 448|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp30 : 464|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp31 : 480|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp32 : 496|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147485571 BMS_Temperatures2: 32 BMS
 SG_ BMS_Temp33 : 0|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp34 : 16|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp35 : 32|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp36 : 48|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp37 : 64|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp38 : 80|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp39 : 96|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp40 : 112|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp41 : 128|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp42 : 144|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp43 : 160|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp44 : 176|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp45 : 192|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp46 : 208|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp47 : 224|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST
 SG_ BMS_Temp48 : 240|16@1- (0.1,0) [-3276.8|3276.7] "DegC"  HOST

BO_ 2147485572 BMS_CellVoltages1: 64 BMS
 SG_ BMS_CellVolt1 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt2 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt3 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt4 : 48|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt5 : 64|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt6 : 80|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt7 : 96|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt8 : 112|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt9 : 128|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt10 : 144|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt11 : 160|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt12 : 176|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt13 : 192|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt14 : 208|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt15 : 224|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt16 : 240|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt17 : 256|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt18 : 272|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt19 : 288|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt20 : 304|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt21 : 320|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt22 : 336|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt23 : 352|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt24 : 368|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt25 : 384|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt26 : 400|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt27 : 416|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt28 : 432|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt29 : 448|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt30 : 464|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt31 : 480|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt32 : 496|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147485573 BMS_CellVoltages2: 64 BMS
 SG_ BMS_CellVolt33 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt34 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt35 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt36 : 48|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt37 : 64|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt38 : 80|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt39 : 96|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt40 : 112|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt41 : 128|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt42 : 144|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt43 : 160|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt44 : 176|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt45 : 192|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt46 : 208|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt47 : 224|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt48 : 240|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt49 : 256|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt50 : 272|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt51 : 288|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt52 : 304|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt53 : 320|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt54 : 336|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt55 : 352|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt56 : 368|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt57 : 384|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt58 : 400|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt59 : 416|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt60 : 432|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt61 : 448|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt62 : 464|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt63 : 480|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt64 : 496|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147485574 BMS_CellVoltages3: 32 BMS
 SG_ BMS_CellVolt65 : 0|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt66 : 16|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt67 : 32|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt68 : 48|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt69 : 64|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt70 : 80|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt71 : 96|16@1+ (1,0) [0|65535] "mV"  HOST
 SG_ BMS_CellVolt72 : 112|16@1+ (1,0) [0|65535] "mV"  HOST

BO_ 2147485575 BMS_OtherVoltages: 32 BMS
 SG_ BMS_Vdfuse : 0|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_VchgPlus : 32|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_VpackPlus : 64|32@1+ (1,0) [0|4294967295] "mV"  HOST
 SG_ BMS_Vcfuse : 96|32@1+ (1,0) [0|4294967295] "mV"  HOST

BO_ 2147485576 BMS_Version: 8 BMS
 SG_ BMS_ChkSum : 0|32@1+ (1,0) [0|4294967295] "Hex"  HOST
 SG_ BMS_PublicVer : 32|24@1+ (1,0) [0|16777215] "Ver"  HOST


BA_DEF_  "MultiplexExtEnabled" ENUM  "No","Yes";
BA_DEF_ BO_  "CANFD_BRS" ENUM  "0","1";
BA_DEF_  "DBName" STRING ;
BA_DEF_  "BusType" STRING ;
BA_DEF_ BU_  "NodeLayerModules" STRING ;
BA_DEF_ BU_  "ECU" STRING ;
BA_DEF_ BU_  "CANoeJitterMax" INT 0 0;
BA_DEF_ BU_  "CANoeJitterMin" INT 0 0;
BA_DEF_ BU_  "CANoeDrift" INT 0 0;
BA_DEF_ BU_  "CANoeStartDelay" INT 0 0;
BA_DEF_ BO_  "VFrameFormat" ENUM  "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_DEF_  "MultiplexExtEnabled" "No";
BA_DEF_DEF_  "CANFD_BRS" "1";
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "ECU" "";
BA_DEF_DEF_  "CANoeJitterMax" 0;
BA_DEF_DEF_  "CANoeJitterMin" 0;
BA_DEF_DEF_  "CANoeDrift" 0;
BA_DEF_DEF_  "CANoeStartDelay" 0;
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_ "BusType" "CAN FD";
BA_ "DBName" "BBU_CANFD";
BA_ "CANFD_BRS" BO_ 2147485568 1;
BA_ "VFrameFormat" BO_ 2147485568 15;
BA_ "CANFD_BRS" BO_ 2147485569 1;
BA_ "VFrameFormat" BO_ 2147485569 15;
BA_ "CANFD_BRS" BO_ 2147485570 1;
BA_ "VFrameFormat" BO_ 2147485570 15;
BA_ "CANFD_BRS" BO_ 2147485571 1;
BA_ "VFrameFormat" BO_ 2147485571 15;
BA_ "CANFD_BRS" BO_ 2147485572 1;
BA_ "VFrameFormat" BO_ 2147485572 15;
BA_ "CANFD_BRS" BO_ 2147485573 1;
BA_ "VFrameFormat" BO_ 2147485573 15;
BA_ "CANFD_BRS" BO_ 2147485574 1;
BA_ "VFrameFormat" BO_ 2147485574 15;
BA_ "CANFD_BRS" BO_ 2147485575 1;
BA_ "VFrameFormat" BO_ 2147485575 15;
BA_ "VFrameFormat" BO_ 2147485576 1;

VAL_ 2147485569 BMS_BatteryStatusLow 15 "ST" 14 "RUN" 13 "COMM_PRESENTF" 12 "KEY_PRESENTF1" 11 "KEY_PRESENTF0" 10 "CHG_PRESENTF1" 9 "CHG_PRESENTF0" 8 "LTCF" 7 "LTUF" 6 "WARRANTY" 5 "BEF" 4 "BFF" 3 "CBF" 2 "DSMF" 1 "CHGF" 0 "ZEROF" ;
VAL_ 2147485569 BMS_BatteryStatusHigh 15 "VSTG1" 14 "VSTG0" 13 "ZERO_DSG" 12 "SC_RST" 11 "BMS_PST_F" 10 "BMS_11VCC_F" 9 "DCHG_OFF_F" 8 "WH_OCV_F" 7 "BMS_RESET_F" 6 "BMS_FAULT_F" 5 "CHG_EN_F" 4 "DCHG_EN_F" 3 "SC_RELEASE_F" 2 "CHG_MODE" 1 "BMS_PRESENT_F" 0 "Reserved" ;
VAL_ 2147485569 BMS_PackStatusLow 15 "FDM_EN" 14 "DFCP" 13 "CFCP" 12 "LVDR" 11 "PRE-DSGMS" 10 "PRE-CHGMS" 9 "DFDP" 8 "CFDP" 7 "FCCFlagUpdate" 6 "EventFlag" 5 "WDTRST" 4 "DFBS" 3 "DFAS" 2 "SEALDFB" 1 "SEALDFA" 0 "PrepUpdateFCC" ;
VAL_ 2147485569 BMS_PackStatusHigh 15 "Reserved" 14 "SOCB" 13 "SMCB" 12 "LT_OCV_prd" 11 "UNPLG_SD" 10 "DFDS" 9 "AccCHG" 8 "LT_OCV_EN" 7 "FLT_INJ" 6 "Data_UT" 5 "RST_CD" 4 "XDFCP" 3 "XCFCP" 2 "XDFDP" 1 "XCFDP" 0 "Pack_Idle" ;
VAL_ 2147485569 BMS_SafetyStatusLow 15 "SD_Fail" 14 "AFE_COMM" 13 "MOSOTUTF" 12 "Reserved" 11 "11V_OVP" 10 "Reserved" 9 "COCF" 8 "DOCF" 7 "Reserved" 6 "DCI" 5 "DUTF" 4 "DOTF" 3 "CUTF" 2 "COTF" 1 "UVPF" 0 "OVPF" ;
VAL_ 2147485569 BMS_SafetyStatusHigh 15 "FLT_INJ_PF" 14 "FDM" 13 "Reserved" 12 "AFEDOC" 11 "DOPP" 10 "DUP" 9 "Reserved" 8 "FETNG_ExtSCP" 7 "FETNG_FETOFF" 6 "FETNG_ALM1" 5 "DOC3" 4 "DOC2" 3 "IRAF" 2 "PDSGF" 1 "Reserved" 0 "HW_DOCF" ;
VAL_ 2147485569 BMS_WarnStatus 15 "Reserved" 14 "COTAF" 13 "MOSOTUTF" 12 "Reserved" 11 "Reserved" 10 "Reserved" 9 "COCF" 8 "DOCF" 7 "Reserved" 6 "Reserved" 5 "DUTF" 4 "DOTF" 3 "CUTF" 2 "COTF" 1 "UVPF" 0 "OVPF" ;
VAL_ 2147485569 BMS_STStatus 15 "Reserved" 14 "MCU_AD" 13 "AADC_T" 12 "INT" 11 "AOSC" 10 "AADC_C" 9 "OVD" 8 "FET" 7 "AADC_V" 6 "OSC" 5 "IDP" 4 "IOP" 3 "ROM" 2 "STA" 1 "RAM" 0 "CPU" ;
VAL_ 2147485569 BMS_PFStatusLow 15 "LBCF" 14 "FET_NG" 13 "Reserved" 12 "OTPF" 11 "Reserved" 10 "CFUSE" 9 "AFE_NG" 8 "FETF2" 7 "AFE_PATAM" 6 "UVPF" 5 "MCUF" 4 "FETF" 3 "P2ND" 2 "OVPF" 1 "FUSE" 0 "CUBF" ;
VAL_ 2147485569 BMS_CBS0_15 15 "Cell-16" 14 "Cell-15" 13 "Cell-14" 12 "Cell-13" 11 "Cell-12" 10 "Cell-11" 9 "Cell-10" 8 "Cell-9" 7 "Cell-8" 6 "Cell-7" 5 "Cell-6" 4 "Cell-5" 3 "Cell-4" 2 "Cell-3" 1 "Cell-2" 0 "Cell-1" ;
VAL_ 2147485569 BMS_PFStatusHigh 15 "Bit15" 14 "Bit14" 13 "Bit13" 12 "Bit12" 11 "Bit11" 10 "Bit10" 9 "Bit9" 8 "Bit8" 7 "Bit7" 6 "Bit6" 5 "Bit5" 4 "Bit4" 3 "Bit3" 2 "Bit2" 1 "Bit1" 0 "Bit0" ;
VAL_ 2147485569 BMS_EngMode 7 "Bit7" 6 "Bit6" 5 "Bit5" 4 "Bit4" 3 "Bit3" 2 "Bit2" 1 "Bit1" 0 "Bit0" ;
